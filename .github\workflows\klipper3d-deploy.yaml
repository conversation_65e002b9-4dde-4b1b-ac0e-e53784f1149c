name: klipper3d deploy
on:
  schedule:
    - cron: "0 0 * * *"
  push:
    branches:
      - master
    paths:
      - docs/**
      - .github/workflows/klipper3d-deploy.yaml
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup python
        uses: actions/setup-python@v4
        with:
          python-version: '3.8'
      - uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('docs/_klipper3d/mkdocs-requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-
      - name: Install dependencies
        run: pip install -r docs/_klipper3d/mkdocs-requirements.txt
      - name: Build MkDocs Pages
        run: docs/_klipper3d/build-translations.sh
      - name: Deploy
        uses: JamesIves/github-pages-deploy-action@v4.4.3
        with:
          branch: gh-pages # The branch the action should deploy to.
          folder: site # The folder the action should deploy.
