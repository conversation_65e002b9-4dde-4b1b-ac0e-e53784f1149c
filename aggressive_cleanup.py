#!/usr/bin/env python3
"""
Aggressive cleanup for 6-axis robotic arm setup
Removes all non-essential modules, keeping only core tool functions
"""

import os
import sys

def safe_remove(path):
    """Safely remove file"""
    try:
        if os.path.exists(path):
            os.remove(path)
            print(f"✓ Deleted: {path}")
        else:
            print(f"✗ Not found: {path}")
    except Exception as e:
        print(f"✗ Error deleting {path}: {e}")

def main():
    if not os.path.exists("klippy/klippy.py"):
        print("Error: Run from Klipper root directory")
        sys.exit(1)
    
    print("🗑️  Aggressive cleanup for 6-axis robotic arm")
    print("This will remove ALL non-essential modules")
    
    response = input("Continue? This will delete many files! (yes/NO): ").strip()
    if response.lower() != 'yes':
        print("Cancelled")
        return
    
    # Files to delete from klippy/extras/
    extras_to_delete = [
        # Motion sensors & accelerometers
        "klippy/extras/adxl345.py",
        "klippy/extras/mpu9250.py", 
        "klippy/extras/lis2dw.py",
        "klippy/extras/lis3dh.py",
        "klippy/extras/icm20948.py",
        "klippy/extras/angle.py",
        "klippy/extras/ldc1612.py",
        
        # Load cells & filament sensors
        "klippy/extras/load_cell.py",
        "klippy/extras/load_cell_probe.py", 
        "klippy/extras/hall_filament_width_sensor.py",
        "klippy/extras/tsl1401cl_filament_width_sensor.py",
        "klippy/extras/filament_motion_sensor.py",
        "klippy/extras/filament_switch_sensor.py",
        
        # Homing & motion
        "klippy/extras/homing.py",
        "klippy/extras/homing_heaters.py", 
        "klippy/extras/homing_override.py",
        "klippy/extras/motion_report.py",
        "klippy/extras/force_move.py",
        
        # Print features
        "klippy/extras/firmware_retraction.py",
        "klippy/extras/gcode_arcs.py",
        "klippy/extras/tuning_tower.py",
        
        # Servos & PWM tools
        "klippy/extras/servo.py",
        "klippy/extras/pwm_tool.py",
        "klippy/extras/pwm_cycle_time.py",
        
        # Statistics & CAN
        "klippy/extras/statistics.py",
        "klippy/extras/canbus_ids.py",
        "klippy/extras/canbus_stats.py",
        
        # Optional sensors (comment out if you need them)
        "klippy/extras/bme280.py",
        "klippy/extras/aht10.py", 
        "klippy/extras/htu21d.py",
        "klippy/extras/sht3x.py",
        "klippy/extras/lm75.py",
        
        # Optional I2C/SPI devices (comment out if you need them)
        "klippy/extras/ad5206.py",
        "klippy/extras/dac084S085.py",
        "klippy/extras/mcp4018.py",
        "klippy/extras/mcp4451.py", 
        "klippy/extras/mcp4728.py",
        "klippy/extras/pca9533.py",
        "klippy/extras/pca9632.py",
        "klippy/extras/sx1509.py",
        
        # Specialized features
        "klippy/extras/bulk_sensor.py",
        "klippy/extras/pulse_counter.py",
        "klippy/extras/sos_filter.py",
        "klippy/extras/samd_sercom.py",
        "klippy/extras/duplicate_pin_override.py",
        "klippy/extras/error_mcu.py",
        "klippy/extras/garbage_collection.py",
    ]
    
    print(f"\n📋 Will delete {len(extras_to_delete)} files from klippy/extras/")
    
    for file_path in extras_to_delete:
        safe_remove(file_path)
    
    # Additional config cleanup
    config_patterns = [
        "config/printer-*.cfg",
        "config/kit-*.cfg", 
    ]
    
    print(f"\n📋 Cleaning config files...")
    import glob
    for pattern in config_patterns:
        for file_path in glob.glob(pattern):
            safe_remove(file_path)
    
    print(f"\n✅ Cleanup complete!")
    print(f"\n📊 Remaining klippy/extras modules:")
    
    remaining = []
    for file in os.listdir("klippy/extras"):
        if file.endswith(".py") and file != "__init__.py":
            remaining.append(file)
    
    remaining.sort()
    for i, module in enumerate(remaining, 1):
        print(f"  {i:2d}. {module}")
    
    print(f"\nTotal remaining: {len(remaining)} modules")
    
    print(f"\n🎯 Core modules preserved:")
    print(f"  - Temperature control (heaters, thermistor, temperature_*)")
    print(f"  - Fan control (fan, fan_generic, controller_fan)")
    print(f"  - Stepper control (extruder_stepper, manual_stepper, stepper_enable)")
    print(f"  - Pin control (buttons, gcode_button, output_pin)")
    print(f"  - G-code support (gcode_macro, gcode_move)")
    print(f"  - TMC drivers (if using TMC steppers)")
    print(f"  - Essential sensors (ds18b20, ads1220, etc.)")

if __name__ == "__main__":
    main()
