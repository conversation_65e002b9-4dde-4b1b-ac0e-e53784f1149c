#!/usr/bin/env python3
"""
Klipper 6-Axis Robotic Arm Cleanup Script
Removes motion planning, bed leveling, input shaping, and display-related modules
while preserving all hardware support, temperature sensors, and stepper drivers.
"""

import os
import shutil
import sys
from pathlib import Path

def confirm_deletion(items, category):
    """Ask user to confirm deletion of items in a category"""
    print(f"\n=== {category} ===")
    for item in items:
        print(f"  {item}")
    
    response = input(f"\nDelete {len(items)} items in '{category}'? (y/N): ").strip().lower()
    return response in ['y', 'yes']

def safe_remove(path):
    """Safely remove file or directory"""
    try:
        if os.path.isfile(path):
            os.remove(path)
            print(f"Deleted file: {path}")
        elif os.path.isdir(path):
            shutil.rmtree(path)
            print(f"Deleted directory: {path}")
        else:
            print(f"Not found (skipping): {path}")
    except Exception as e:
        print(f"Error deleting {path}: {e}")

def main():
    # Verify we're in klipper root directory
    if not os.path.exists("klippy/klippy.py"):
        print("Error: This script must be run from the Klipper root directory")
        sys.exit(1)
    
    print("Klipper 6-Axis Robotic Arm Cleanup")
    print("This will remove motion planning, bed leveling, input shaping, and display modules")
    print("Hardware support (MCU/sensors/drivers) will be preserved")
    
    # Define deletion categories
    categories = {
        "Motion Planning & Bed Leveling (klippy/extras)": [
            "klippy/extras/axis_twist_compensation.py",
            "klippy/extras/bed_mesh.py",
            "klippy/extras/bed_screws.py",
            "klippy/extras/bed_tilt.py",
            "klippy/extras/delta_calibrate.py",
            "klippy/extras/endstop_phase.py",
            "klippy/extras/manual_probe.py",
            "klippy/extras/probe.py",
            "klippy/extras/probe_eddy_current.py",
            "klippy/extras/quad_gantry_level.py",
            "klippy/extras/safe_z_home.py",
            "klippy/extras/screws_tilt_adjust.py",
            "klippy/extras/skew_correction.py",
            "klippy/extras/z_tilt.py",
            "klippy/extras/z_thermal_adjust.py",
        ],
        
        "Input Shaping & Resonance (klippy/extras)": [
            "klippy/extras/input_shaper.py",
            "klippy/extras/resonance_tester.py",
            "klippy/extras/shaper_calibrate.py",
            "klippy/extras/shaper_defs.py",
        ],
        
        "Display System (klippy/extras)": [
            "klippy/extras/display",
            "klippy/extras/display_status.py",
        ],
        
        "Print Management (klippy/extras)": [
            "klippy/extras/exclude_object.py",
            "klippy/extras/pause_resume.py",
            "klippy/extras/print_stats.py",
            "klippy/extras/virtual_sdcard.py",
            "klippy/extras/sdcard_loop.py",
        ],
        
        "LED & Decorative (klippy/extras)": [
            "klippy/extras/dotstar.py",
            "klippy/extras/neopixel.py",
            "klippy/extras/led.py",
        ],
        
        "Special Hardware (klippy/extras)": [
            "klippy/extras/palette2.py",
            "klippy/extras/replicape.py",
            "klippy/extras/smart_effector.py",
        ],
        
        "Kinematics (except none & extruder)": [
            "klippy/kinematics/cartesian.py",
            "klippy/kinematics/corexy.py",
            "klippy/kinematics/corexz.py",
            "klippy/kinematics/delta.py",
            "klippy/kinematics/deltesian.py",
            "klippy/kinematics/generic_cartesian.py",
            "klippy/kinematics/hybrid_corexy.py",
            "klippy/kinematics/hybrid_corexz.py",
            "klippy/kinematics/idex_modes.py",
            "klippy/kinematics/polar.py",
            "klippy/kinematics/rotary_delta.py",
            "klippy/kinematics/winch.py",
            "klippy/kinematics/kinematic_stepper.py",
        ],
    }
    
    # Additional categories for docs and config
    doc_categories = {
        "Motion/Calibration Docs": [
            "docs/Axis_Twist_Compensation.md",
            "docs/Bed_Level.md",
            "docs/Bed_Mesh.md",
            "docs/Delta_Calibrate.md",
            "docs/Endstop_Phase.md",
            "docs/Kinematics.md",
            "docs/Manual_Level.md",
            "docs/Multi_MCU_Homing.md",
            "docs/Probe_Calibrate.md",
            "docs/Skew_Correction.md",
        ],

        "Resonance/Shaping Docs": [
            "docs/Measuring_Resonances.md",
            "docs/Resonance_Compensation.md",
        ],

        "Print Management Docs": [
            "docs/Exclude_Object.md",
            "docs/SDCard_Updates.md",
        ],

        "Example Configs (keep example.cfg & example-extras.cfg)": [
            "config/generic-*.cfg",
            "config/example-cartesian.cfg",
            "config/example-corexy.cfg",
            "config/example-corexz.cfg",
            "config/example-delta.cfg",
            "config/example-deltesian.cfg",
            "config/example-hybrid-corexy.cfg",
            "config/example-hybrid-corexz.cfg",
            "config/example-polar.cfg",
            "config/example-rotary-delta.cfg",
            "config/example-winch.cfg",
        ],

        "Analysis Scripts": [
            "scripts/calibrate_shaper.py",
            "scripts/graph_accelerometer.py",
            "scripts/graph_shaper.py",
            "scripts/graph_motion.py",
            "scripts/graph_mesh.py",
            "scripts/filter_workbench.ipynb",
        ],
    }

    # Process main categories
    for category, items in categories.items():
        if confirm_deletion(items, category):
            for item in items:
                safe_remove(item)
        else:
            print(f"Skipped: {category}")

    # Process documentation and config categories
    for category, items in doc_categories.items():
        if confirm_deletion(items, category):
            for item in items:
                if "*" in item:
                    # Handle wildcards
                    import glob
                    for path in glob.glob(item):
                        safe_remove(path)
                else:
                    safe_remove(item)
        else:
            print(f"Skipped: {category}")

    print("\n=== Cleanup Summary ===")
    print("Preserved:")
    print("  - All MCU/hardware support (src/, lib/)")
    print("  - All temperature sensors & stepper drivers")
    print("  - All TMC stepper drivers")
    print("  - Heater/fan control")
    print("  - Testing framework")
    print("  - Core klippy modules")
    print("  - klippy/kinematics/none.py & extruder.py")
    print("  - Essential docs (Installation, Config_Reference, G-Codes, etc.)")

    print("\nRemoved:")
    print("  - Motion planning & bed leveling")
    print("  - Input shaping & resonance compensation")
    print("  - Display system")
    print("  - Print management features")
    print("  - Traditional kinematics")
    print("  - Analysis/graphing scripts")

    print("\nNext steps:")
    print("  1. Test compilation: cd src && make menuconfig && make")
    print("  2. Test klippy startup with kinematics=none")
    print("  3. Add WAIT_PIN extension for hardware handshake")
    print("  4. Create custom config for 6-axis setup")

if __name__ == "__main__":
    main()
