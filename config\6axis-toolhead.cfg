# Configuration for 6-Axis Robotic Arm 3D Printing Toolhead
# This config focuses only on tool functions (heater/extruder/handshake)
# Motion planning is handled by external robotic arm controller

[mcu]
# Replace with your MCU connection
serial: /dev/serial/by-id/usb-Klipper_stm32f446xx_XXXXXX

# Use 'none' kinematics since motion is handled by robotic arm
[printer]
kinematics: none
max_velocity: 1000
max_accel: 1000

#####################################################################
# Temperature Control
#####################################################################

[extruder]
# Extruder stepper configuration
step_pin: PF9
dir_pin: PF10
enable_pin: !PG2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.75

# Heater configuration
heater_pin: PA2
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF4
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF3
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

#####################################################################
# Fan Control
#####################################################################

[fan]
# Part cooling fan
pin: PA8

[heater_fan hotend_fan]
# Hotend fan
pin: PE5
heater: extruder
heater_temp: 50.0

[controller_fan controller_fan]
# Controller board fan
pin: PD12

#####################################################################
# Hardware Handshake for Robotic Arm
#####################################################################

[wait_pin]
# Enable WAIT_PIN functionality

# Example handshake pin from robotic arm DOUT
# Connect robotic arm DOUT signal to this MCU pin
# handshake_pin: PA0

#####################################################################
# G-code Macros for 6-Axis Printing
#####################################################################

[gcode_macro START_PRINT]
description: Initialize toolhead for 6-axis printing
gcode:
    {% set BED_TEMP = params.BED_TEMP|default(60)|float %}
    {% set EXTRUDER_TEMP = params.EXTRUDER_TEMP|default(200)|float %}
    
    # Start heating
    M140 S{BED_TEMP}
    M104 S{EXTRUDER_TEMP}
    
    # Wait for temperatures
    M190 S{BED_TEMP}
    M109 S{EXTRUDER_TEMP}
    
    # Reset extruder
    G92 E0
    
    RESPOND MSG="Toolhead ready for 6-axis printing"

[gcode_macro SYNC_EXTRUDE]
description: Wait for robotic arm signal and extrude
gcode:
    {% set AMOUNT = params.AMOUNT|default(0.5)|float %}
    {% set SPEED = params.SPEED|default(300)|float %}
    {% set PIN = params.PIN|default("PA0")|string %}
    {% set TIMEOUT = params.TIMEOUT|default(10)|float %}
    
    # Wait for robotic arm handshake signal
    WAIT_PIN PIN={PIN} STATE=1 TIMEOUT={TIMEOUT}
    
    # Execute extrusion
    G1 E{AMOUNT} F{SPEED}
    
    # Optional: Reset handshake (if using level-triggered)
    # WAIT_PIN PIN={PIN} STATE=0 TIMEOUT=1

[gcode_macro SYNC_RETRACT]
description: Wait for robotic arm signal and retract
gcode:
    {% set AMOUNT = params.AMOUNT|default(0.5)|float %}
    {% set SPEED = params.SPEED|default(600)|float %}
    {% set PIN = params.PIN|default("PA0")|string %}
    {% set TIMEOUT = params.TIMEOUT|default(10)|float %}
    
    # Wait for robotic arm handshake signal
    WAIT_PIN PIN={PIN} STATE=1 TIMEOUT={TIMEOUT}
    
    # Execute retraction
    G1 E-{AMOUNT} F{SPEED}

[gcode_macro END_PRINT]
description: Cleanup after 6-axis printing
gcode:
    # Turn off heaters
    M104 S0
    M140 S0
    
    # Turn off fans
    M106 S0
    
    # Reset extruder
    G92 E0
    
    RESPOND MSG="6-axis printing completed"

#####################################################################
# Status and Monitoring
#####################################################################

[gcode_macro STATUS_TOOLHEAD]
description: Report toolhead status
gcode:
    {% set extruder = printer.extruder %}
    {% set heater_bed = printer.heater_bed %}
    
    RESPOND MSG="=== Toolhead Status ==="
    RESPOND MSG="Extruder: {extruder.temperature:.1f}°C (target: {extruder.target:.1f}°C)"
    RESPOND MSG="Bed: {heater_bed.temperature:.1f}°C (target: {heater_bed.target:.1f}°C)"
    
    {% if printer.wait_pin %}
        RESPOND MSG="WAIT_PIN: {printer.wait_pin.monitored_pins} pins monitored"
    {% endif %}

#####################################################################
# Emergency and Safety
#####################################################################

[gcode_macro EMERGENCY_STOP]
description: Emergency stop for toolhead
gcode:
    M112  # Emergency stop

[gcode_macro PAUSE_TOOLHEAD]
description: Pause toolhead operations
gcode:
    # Turn off heaters
    M104 S0
    M140 S0
    # Keep fans running for cooling
    RESPOND MSG="Toolhead paused"

[gcode_macro RESUME_TOOLHEAD]
description: Resume toolhead operations
gcode:
    {% set EXTRUDER_TEMP = params.EXTRUDER_TEMP|default(200)|float %}
    {% set BED_TEMP = params.BED_TEMP|default(60)|float %}
    
    # Restart heating
    M104 S{EXTRUDER_TEMP}
    M140 S{BED_TEMP}
    
    RESPOND MSG="Toolhead resuming..."

#####################################################################
# Testing and Calibration
#####################################################################

[gcode_macro TEST_HANDSHAKE]
description: Test hardware handshake functionality
gcode:
    {% set PIN = params.PIN|default("PA0")|string %}
    {% set TIMEOUT = params.TIMEOUT|default(5)|float %}
    
    RESPOND MSG="Testing handshake on pin {PIN}..."
    RESPOND MSG="Please trigger the signal within {TIMEOUT} seconds"
    
    WAIT_PIN PIN={PIN} STATE=1 TIMEOUT={TIMEOUT}
    RESPOND MSG="Handshake signal received!"
    
    # Wait for signal to go low
    WAIT_PIN PIN={PIN} STATE=0 TIMEOUT={TIMEOUT}
    RESPOND MSG="Handshake test completed"

[gcode_macro CALIBRATE_EXTRUSION]
description: Calibrate extrusion amount
gcode:
    {% set AMOUNT = params.AMOUNT|default(10)|float %}
    
    RESPOND MSG="Calibrating extrusion: {AMOUNT}mm"
    G92 E0
    G1 E{AMOUNT} F300
    G92 E0
    RESPOND MSG="Extrusion calibration completed"
