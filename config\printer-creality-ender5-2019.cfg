# This file contains common pin mappings for the 2019 Creality
# Ender 5. To use this config, the firmware should be compiled for the
# AVR atmega1284p. This also works for the v1.1.5 silent boards.

# Note, a number of Melzi boards are shipped with a bootloader that
# requires the following command to flash the board:
#  avrdude -p atmega1284p -c arduino -b 57600 -P /dev/ttyUSB0 -U out/klipper.elf.hex
# For v1.1.5 silent boards, the following command is used:
#  avrdude -p atmega1284p -c arduino -P /dev/ttyUSB0 -b 115200 -U flash:w:out/klipper.elf.hex
# If the above command does not work and "make flash" does not work
# then one may need to flash a bootloader to the board - see the
# Klipper docs/Bootloaders.md file for more information.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD7
dir_pin: !PC5
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC2
position_endstop: 235
position_max: 235
homing_speed: 30

[stepper_y]
step_pin: PC6
dir_pin: !PC7
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC3
position_endstop: 235
position_max: 235
homing_speed: 30

[stepper_z]
step_pin: PB3
dir_pin: !PB2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8 # Use 4 for Ender5 versions after late 2019
endstop_pin: ^PC4
position_endstop: 0.0
position_max: 300

[extruder]
max_extrude_only_distance: 100.0
step_pin: PB1
dir_pin: !PB0
enable_pin: !PD6
microsteps: 16
rotation_distance: 33.683
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA7
control: pid
# tuned for stock hardware with 200 degree Celsius target
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PD4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA6
control: pid
# tuned for stock hardware with 50 degree Celsius target
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: PB4

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0
# Silent boards tend to have the exact same serial ID, except without USB2.0, using USB instead.
# e.g. /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PA3
sclk_pin: PA1
sid_pin: PC1
encoder_pins: ^PD2, ^PD3
click_pin: ^!PC0
