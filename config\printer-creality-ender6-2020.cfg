# This file contains pin mappings for the stock 2020 Creality Ender 6.
# To use this config, during "make menuconfig" select the STM32F103
# with a "28KiB bootloader" and serial (on USART1 PA10/PA9)
# communication.

# Because this printer has factory wiring, mounts, and firmware for
# a BLTouch, but does not ship with one at this time, default values
# for the sensor have been specified, but disabled, in anticipation of
# future revisions or user modification. User should take care to
# customize the offsets, particularly z-offset, for their specific unit.

# If you prefer a direct serial connection, in "make menuconfig"
# select "Enable extra low-level configuration options" and select
# serial (on USART3 PB11/PB10), which is broken out on the 10 pin IDC
# cable used for the LCD module as follows:
# 3: Tx, 4: Rx, 9: GND, 10: VCC

# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PB8
dir_pin: PB7
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA5
position_endstop: 260
position_max: 260
homing_speed: 50

[stepper_y]
step_pin: PC2
dir_pin: !PB9
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA6
position_endstop: 260
position_max: 260
homing_speed: 50

[stepper_z]
step_pin: PB6
dir_pin: PB5
enable_pin: !PC3
microsteps: 16
rotation_distance: 8
position_endstop: 0.0                     # disable to use BLTouch
endstop_pin: ^PA7                         # disable to use BLTouch
# endstop_pin: probe:z_virtual_endstop    # enable to use BLTouch
# position_min: -5                        # enable to use BLTouch
position_max: 400

# [safe_z_home]                           # enable for BLTouch
# home_xy_position: 150.7, 137
# speed: 100
# z_hop: 10
# z_hop_speed: 5

# [bltouch]                               # enable for BLTouch
# sensor_pin: ^PB1
# control_pin: PB0
# x_offset: -20.7
# y_offset: -7
# z_offset: 2.4
# speed: 3.0

# [bed_mesh]                              # enable for BLTouch
# speed: 100
# mesh_min: 10, 10
# mesh_max: 239, 239
# algorithm: bicubic
# probe_count: 5, 5

[extruder]
max_extrude_only_distance: 1000.0
step_pin: PB4
dir_pin: !PB3
enable_pin: !PC3
microsteps: 16
rotation_distance: 22.857
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid
pid_Kp: 26.949
pid_Ki: 1.497
pid_Kd: 121.269
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PA2
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_Kp: 327.11
pid_Ki: 19.20
pid_Kd: 1393.45
min_temp: 0
max_temp: 100

[fan]
pin: PA0

[filament_switch_sensor e0_sensor]
switch_pin: PA4

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: corexy
max_velocity: 500
max_accel: 2000
max_z_velocity: 10
max_z_accel: 100
