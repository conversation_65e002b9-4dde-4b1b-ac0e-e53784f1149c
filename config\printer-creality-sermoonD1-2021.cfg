# This file contains pin mappings for the Creality Sermoon D1 with
# Rev. 4.3.1 Motherboard.

# To use this config, during "make menuconfig" select the STM32F103
# with a "28KiB bootloader" and serial (on USART1 PA10/PA9)
# communication.

# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC2
dir_pin: !PB9
enable_pin: !PC3
rotation_distance: 40
microsteps: 16
endstop_pin: ^PA5
position_min: -5
position_endstop: -5
position_max: 280
homing_speed: 50

[stepper_y]
step_pin: PB8
dir_pin: PB7
enable_pin: !PC3
rotation_distance: 40
microsteps: 16
endstop_pin: ^PA6
position_min: -2
position_endstop: -2
position_max: 260
homing_speed: 50

[stepper_z]
step_pin: PB6
dir_pin: PB5
enable_pin: !PC3
rotation_distance: 8
microsteps: 16
position_endstop: 0.0                     # disable to use BLTouch
endstop_pin: ^PA7                         # disable to use BLTouch
# endstop_pin: probe:z_virtual_endstop    # enable to use BLTouch
# position_min: -5                        # enable to use BLTouch
position_max: 300
homing_speed: 4
second_homing_speed: 1
homing_retract_dist: 2.0

[extruder]
max_extrude_only_distance: 1000.0
step_pin: PB4
dir_pin: PB3
enable_pin: !PC3
microsteps: 16
rotation_distance: 34.936
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid
pid_kp: 26.163
pid_ki: 1.418
pid_kd: 120.675
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PA2
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_Kp: 76.301
pid_Ki: 1.707
pid_Kd: 852.669
min_temp: 0
max_temp: 100

[fan]
pin: PA0
kick_start_time: 0.5

[mcu]
#use command ls /dev/serial/by-id/* to find the printer connected via USB
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

# Before printing the PROBE_CALIBRATE command needs to be issued
# to run the probe calibration procedure, described at
# docs/Probe_Calibrate.md, to find the correct z_offset.

# [safe_z_home]                           # enable for BLTouch
# home_xy_position: 150.7, 137
# speed: 100
# z_hop: 10
# z_hop_speed: 5

# [bltouch]
# sensor_pin: ^PB1
# control_pin: PB0
# x_offset: 20
# y_offset: 0
# z_offset: 2
# speed: 3.0

# [bed_mesh]                              # enable for BLTouch
# speed: 100
# mesh_min: 20, 15
# mesh_max: 250, 240
# algorithm: bicubic
# probe_count: 5, 5

[filament_switch_sensor filament_sensor]
pause_on_runout: true
switch_pin: !PA4

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 500
max_z_velocity: 5
max_z_accel: 100
