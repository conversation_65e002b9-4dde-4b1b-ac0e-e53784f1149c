# This file contains standard pin mappings for the stock Elegoo Neptune 2 with a
# ZNP Robin Nano (v 1.2 and v1.3) board.
# For the 1.2 board:
#   - Compile with the processor model STM32F103.
#   - Enable "extra low-level configuration options"
#   - Select the 28KiB bootloader,
#   - Select (Serial (on # USART3 PB11/PB10) for the communication interface.
# Note that the "make flash" command does not work with ZNP Robin boards. After
# running "make", run the following command:
# ./scripts/update_mks_robin.py out/klipper.bin out/elegoo.bin
#
# For the 1.3 board:
#   - Compile with the processor model STM32F407.
#   - Enable "extra low-level configuration options"
#   - Select the 32KiB bootloader,
#   - Select (Serial (on # USART3 PB11/PB10) for the communication interface.
# Note that the "make flash" command does not work with ZNP Robin boards. After
# running "make", run the following command:
# cp out/klipper.bin out/elegoo.bin
#
# Copy the file out/elegoo.bin to an SD card and then restart the printer with
# that SD card.
# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE3
dir_pin: PE2
enable_pin: !PE4
microsteps: 16
rotation_distance: 40
endstop_pin: PA15
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PE0
dir_pin: PB9
enable_pin: !PE1
microsteps: 16
rotation_distance: 40
endstop_pin: PA12
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PB5
dir_pin: !PB4
enable_pin: !PB8
microsteps: 16
rotation_distance: 8
endstop_pin: PA11
position_endstop: 0.0
position_max: 250

[extruder]
max_extrude_only_distance: 100.0
step_pin: PD6
dir_pin: PD3
enable_pin: !PB3
microsteps: 16
rotation_distance: 34.406 # use 23.809 as a base before calibration for a 2s
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
# tuned for stock hardware with 210 degree Celsius target
control: pid
pid_Kp: 29.056
pid_Ki: 1.628
pid_Kd: 129.664
min_temp: 0
max_temp: 250

[filament_switch_sensor filament_sensor]
pause_on_runout: True
switch_pin: PA4

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
# tuned for stock hardware with 60 degree Celsius target
control: pid
pid_Kp: 70.857
pid_Ki: 1.221
pid_Kd: 1028.316
min_temp: 0
max_temp: 110

[heater_fan hotend_fan]
pin: PB0
heater: extruder
heater_temp: 50.0

[fan]
pin: PB1

[mcu]
serial: /dev/ttyUSB0
restart_method: command

[bed_screws]
screw1: 32.5, 32.5
screw2: 32.5, 202.5
screw3: 202.5, 32.5
screw4: 202.5, 202.5

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output display_reset]
# the FSMC touchscreen isn't supported, so we'll just disable it
pins: !PC6, !PD13
