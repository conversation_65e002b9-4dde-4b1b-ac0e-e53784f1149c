# This file contains common pin mappings for the Eryone ER-20 board
# board. To use this config, the firmware should be compiled for the
# STM32F103 with a 16KiB HID Bootloader.

# Extra settings to support BL Touch, Filament Runout sensor and
# have setting for fixing GCode errors, these configus can be found on
# Eryone's Facebook Group under files.
# Facebook like to files: https://www.facebook.com/groups/247271792709370/files

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PB15
dir_pin: PB14
rotation_distance: 40
enable_pin: !PD10
microsteps: 16
endstop_pin: tmc2209_stepper_x:virtual_endstop
position_endstop: 0
position_max: 250
homing_speed: 40
homing_retract_dist: 0

[tmc2209 stepper_x]
uart_pin: PC11
tx_pin: PC10
diag_pin: ^PD8
uart_address: 2
run_current: 0.6
stealthchop_threshold: 999999
driver_SGTHRS: 80

[stepper_y]
step_pin: PD14
dir_pin: !PD13
enable_pin: !PC6
microsteps: 16
rotation_distance: 40
endstop_pin: tmc2209_stepper_y:virtual_endstop
position_min: -9
position_endstop: -9
position_max: 235
homing_speed: 50
homing_retract_dist: 0

[tmc2209 stepper_y]
uart_pin: PC11
tx_pin: PC10
diag_pin: ^PD15
uart_address: 3
run_current: 0.7
stealthchop_threshold: 999999
driver_SGTHRS: 100

[stepper_z]
step_pin: PC8
dir_pin: !PC7
enable_pin: !PA8
microsteps: 16
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
position_max: 250
position_min: -1

[tmc2209 stepper_z]
uart_pin: PC11
tx_pin: PC10
diag_pin: ^PC9
uart_address: 1
run_current: 0.7
stealthchop_threshold: 999999
driver_SGTHRS: 0

[extruder]
step_pin: PE13
dir_pin: PE14
enable_pin: !PB13
microsteps: 16
# Stock extruder [200 steps/rot]*[16 steps]/[93 steps/mm]
rotation_distance: 34.4
nozzle_diameter: 0.4
filament_diameter: 1.75
max_extrude_only_distance: 150
heater_pin: PD11
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
pid_kp: 26.11
pid_ki: 2.01
pid_kd: 85
min_temp: 0
max_temp: 250

[tmc2209 extruder]
uart_pin: PC11
tx_pin: PC10
uart_address: 0
run_current: 0.7
stealthchop_threshold: 999999

[heater_bed]
heater_pin: PD12
sensor_type: ATC Semitec 104GT-2
sensor_pin: PC2
control: pid
pid_kp: 100.12
pid_ki: 5
pid_kd: 305
min_temp: 0
max_temp: 100

[fan]
pin: PB5

[heater_fan heatbreak_cooling_fan]
pin: PB4

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1500
max_z_velocity: 5
max_z_accel: 100

######################################################################
#  Probing/Mesh Leveling settings for stock BL Touch.
#  Please make sure you set the correct values for your offsets.
######################################################################

[bltouch]
sensor_pin: ^PA11
control_pin: PA12
pin_move_time: 0.700
x_offset: 34
y_offset: 0
z_offset: 2.6
pin_up_touch_mode_reports_triggered: False

[safe_z_home]
home_xy_position: 5, 5
z_hop: 5.0
z_hop_speed: 5.0

[bed_mesh]
speed: 80
mesh_min: 40, 20
mesh_max: 210, 205
probe_count: 5, 5

######################################################################
# "RepRapDiscount 128x64 Full Graphic Smart Controller" type display
######################################################################

[display]
lcd_type: st7920
cs_pin: EXP1_4
sclk_pin: EXP1_5
sid_pin: EXP1_3
encoder_pins: ^EXP2_3, ^EXP2_5
click_pin: ^!EXP1_2
#kill_pin: ^!EXP2_8

[output_pin beeper]
pin: EXP1_1

######################################################################
# EXP1 / EXP2 (display) pins
######################################################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PE12, EXP1_3=PE10, EXP1_5=PE8, EXP1_7=PB2,
    EXP1_2=PE11, EXP1_4=PE9, EXP1_6=PE7, EXP1_8=PB1,
    # EXP2 header
    EXP2_3=PE4, EXP2_5=PE3
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi2"
