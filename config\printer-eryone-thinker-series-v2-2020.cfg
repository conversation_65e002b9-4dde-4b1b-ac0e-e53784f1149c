# This file contains pin mappings for the Eryone Thinker Series V2.
# To use this config, the firmware should be compiled for the AVR atmega2560.

# Extra settings to support BL Touch, Filament Runout sensor, TMC2209 and
# have setting for fixing GCode errors, these configus can be found on
# Eryone's Facebook Group under files.
# Facebook like to files: https://www.facebook.com/groups/247271792709370/files
# Config file: printer.cfg.ThinkS-BLTouch-Runout-TMC2209-dapostol73.txt

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC0
dir_pin: !PL1
enable_pin: !PA7
microsteps: 16
rotation_distance: 40
endstop_pin: !PB6
position_min: -5
position_endstop: -5
position_max: 300
homing_speed: 50

[stepper_y]
step_pin: PC1
dir_pin: PL0
enable_pin: !PA6
microsteps: 16
rotation_distance: 40
endstop_pin: !PB5
position_min: -5
position_endstop: -5
position_max: 300
homing_speed: 50

[stepper_z]
step_pin: PC2
dir_pin: PL2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
position_max: 400
homing_speed: 5
endstop_pin: !PB4
position_endstop: 0

[extruder]
step_pin: PC3
dir_pin: !PL6
enable_pin: !PA4
microsteps: 16
# Stock extruder [200 steps/rot]*[16 steps]/[93 steps/mm]
rotation_distance: 34.4
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PH6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF0
control: pid
pid_kp: 22.2
pid_ki: 1.08
pid_kd: 144
min_temp: 0
max_temp: 250
max_extrude_only_distance: 300

[fan]
pin: PH5

[heater_fan heatbreak_cooling_fan]
pin: PH3

[heater_bed]
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF2
control: pid
pid_kp: 100.0
pid_ki: 5.023
pid_kd: 305.4
min_temp: 0
max_temp: 100

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1500
max_z_velocity: 5
max_z_accel: 150

######################################################################
# "RepRapDiscount 128x64 Full Graphic Smart Controller" type displays
######################################################################

[display]
lcd_type: st7920
cs_pin: EXP1_4
sclk_pin: EXP1_5
sid_pin: EXP1_3
encoder_pins: ^EXP2_3, ^EXP2_5
click_pin: ^!EXP1_2
#kill_pin: ^!EXP2_8

[output_pin beeper]
pin: EXP1_1
pwm: True
value: 0
shutdown_value: 0
cycle_time: 0.001
scale: 1000

# Enable 16 micro-steps on steppers X, Y, Z, E0
[static_digital_output stepper_config]
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4

[static_digital_output yellow_led]
pins: !PB7

# Common EXP1 / EXP2 (display) pins
[board_pins]
aliases:
    # Common EXP1/EXP2 headers found on RAMBo v1.4
    EXP1_1=PE6, EXP1_3=PG3, EXP1_5=PJ2, EXP1_7=PJ7, EXP1_9=<GND>,
    EXP1_2=PE2, EXP1_4=PG4, EXP1_6=PJ3, EXP1_8=PJ4, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB3, EXP2_3=PK2, EXP2_5=PK1, EXP2_7=PD4, EXP2_9=<GND>,
    EXP2_2=PB1, EXP2_4=PB0, EXP2_6=PB2, EXP2_8=PE7, EXP2_10=PH2
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi"
