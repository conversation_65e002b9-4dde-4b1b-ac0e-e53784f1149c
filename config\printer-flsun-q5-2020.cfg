# This file contains common configurations and pin mappings
# for the Flsun Q5 using the MKS Robin Nano board.

# To use this config, the firmware should be compiled for the
# STM32F103. When running "make menuconfig", enable "extra low-level
# configuration setup", select the 28KiB bootloader, and serial (on
# USART3 PB11/PB10) communication.

# Note that the "make flash" command does not work with MKS Robin
# boards. After running "make", run the following command:
# ./scripts/update_mks_robin.py out/klipper.bin out/Robin_nano.bin
# Copy the file out/Robin_nano.bin to an SD card and then restart the
# printer with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: delta
max_velocity: 250
max_accel: 3000
max_z_velocity: 100
delta_radius: 107.5
print_radius: 100

[stepper_a]
step_pin: PE3
dir_pin: PE2
enable_pin: !PE4
microsteps: 16
rotation_distance: 40
endstop_pin: PA15
homing_speed: 20
homing_retract_dist: 5
homing_retract_speed: 10
second_homing_speed: 2
position_endstop: 220
arm_length: 215
angle: 210

[stepper_b]
step_pin: PE0
dir_pin: PB9
enable_pin: !PE1
microsteps: 16
rotation_distance: 40
endstop_pin: PA12
angle: 330

[stepper_c]
step_pin: PB5
dir_pin: PB4
enable_pin: !PB8
microsteps: 16
rotation_distance: 40
endstop_pin: PC4
angle: 90

[probe]
pin: !PA11
x_offset: 0
y_offset: 0
z_offset: 20.26
speed: 5.0
samples: 5
samples_result: average
sample_retract_dist: 3
samples_tolerance: 0.02
samples_tolerance_retries: 5

[delta_calibrate]
radius: 95
horizontal_move_z: 25
Speed: 10

[extruder]
step_pin: PD6
dir_pin: !PD3
enable_pin: !PB3
microsteps: 16
rotation_distance: 22.17
gear_ratio: 66:22
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
pid_kp: 14.529
pid_ki: 0.557
pid_kd: 94.802
min_temp: 0
max_temp: 250
max_extrude_only_distance: 110.0

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_Kp: 325.10
pid_Ki: 63.35
pid_Kd: 417.10
min_temp: 0
max_temp: 130

[fan]
pin: PB1
