# This file contains common configurations and pin mappings
# for the Flsun QQ-S using the MKS Robin Mini board.
#
# To use this config, the firmware should be compiled for the
# STM32F103. When running "make menuconfig", enable "extra low-level
# configuration setup", select the 28KiB bootloader, and serial (on
# USART3 PB11/PB10) communication.

# Note that the "make flash" command does not work with MKS Robin
# boards. After running "make", run the following command:
# ./scripts/update_mks_robin.py out/klipper.bin out/Robin_mini.bin
# Copy the file out/Robin_mini.bin to an SD card and then restart the
# printer with that SD card.

# For more detailed instructions on how to set Klipper up for this printer,
# follow this guide: https://github.com/CobraPi/Klipper-Firmware-FLSUN-QQ-S-Pro

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: delta
max_velocity: 500
max_accel: 3000
max_z_velocity: 200
delta_radius: 130
print_radius: 125

[stepper_a]
step_pin: PE3
dir_pin: PE2
enable_pin: !PE4
rotation_distance: 32
endstop_pin: PA15
microsteps: 16
homing_speed: 60
position_endstop: 370
arm_length: 280.0

[stepper_b]
step_pin: PE0
dir_pin: PB9
enable_pin: !PE1
rotation_distance: 32
endstop_pin: PA12
microsteps: 16

[stepper_c]
step_pin: PB5
dir_pin: PB4
enable_pin: !PB8
rotation_distance: 32
endstop_pin: PC4
microsteps: 16

[probe]
pin: !PA11
x_offset: 0
y_offset: 0
z_offset: 18
samples: 3
samples_result: average
sample_retract_dist: 5
samples_tolerance: 0.02
samples_tolerance_retries: 5

[delta_calibrate]
radius: 125
horizontal_move_z: 25

[extruder]
step_pin: PD6
dir_pin: !PD3
enable_pin: !PB3
rotation_distance: 9
microsteps: 16
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
pid_Kp: 14.529
pid_Ki: 0.557
pid_Kd: 94.802
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_Kp: 325.10
pid_Ki: 63.35
pid_Kd: 417.10
min_temp: 0
max_temp: 150

[fan]
pin: PB1
