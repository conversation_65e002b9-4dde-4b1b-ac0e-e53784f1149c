# This file contains common configurations and pin mappings for the FOKOOS
# Odin-5 F3, which uses the MKS Robin Nano v1.2 board. The printer's TFT
# touchscreen is not currently supported by <PERSON><PERSON><PERSON>, so the touchscreen will
# permanently display "Booting..." after the Klipper firmware is flashed; use
# Fluidd, <PERSON>sail, or OctoPrint etc. to control the printer.

# To use this config, the firmware should be compiled for the STM32F103. When
# running "make menuconfig", enable "extra low-level configuration setup",
# select the 28KiB bootloader, and serial (on USART3 PB11/PB10) communication.
# Connect the printer to your Raspberry Pi using the printer's USB port.

# Note that the "make flash" command does not work with MKS Robin
# boards. After running "make", run the following command:
#   ./scripts/update_mks_robin.py out/klipper.bin out/Robin_nano35.bin
# Copy the file out/Robin_nano35.bin to an SD card and then restart the
# printer with that SD card. The firmware will automatically be flashed.

# WARNING: The printer's Z endstop inductive sensor is not very accurate, so
# stepper_z:position_endstop has been set to 0.0, for safety, below. This will
# prevent the nozzle crashing into the bed when you first home Z. You must
# follow <PERSON><PERSON><PERSON>'s instructions for manually calibrating the Z endstop offset
# before you print anything. Be sure to leave room for a margin of error when
# you choose this parameter, i.e. choose a value 0.2-0.3mm smaller than the
# output of the calibration. For example, you might find the actual Z endstop
# position is at 0.5mm, in which case you could set the value to 0.2mm. It is
# recommended to manually level the bed before each print.

# This configuration does support the addition of a BLtouch. See below for
# further instructions.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 250
max_accel: 1000
max_z_velocity: 25
max_z_accel: 100

[bed_screws]
screw1: 33, 33
screw2: 203, 33
screw3: 203, 201
screw4: 33, 201

[stepper_x]
step_pin: PE3
dir_pin: !PE2
enable_pin: !PE4
microsteps: 16
# full_steps_per_rotation: 200
# rotation_distance = <full_steps_per_rotation> * <microsteps> / <steps_per_mm>
# 200 * 16 / 80 = 40
rotation_distance: 40
endstop_pin: !PA15
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PE0
dir_pin: !PB9
enable_pin: !PE1
microsteps: 16
# full_steps_per_rotation: 200
# rotation_distance = <full_steps_per_rotation> * <microsteps> / <steps_per_mm>
# 200 * 16 / 80 = 40
rotation_distance: 40
endstop_pin: PA12
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PB5
dir_pin: PB4
enable_pin: !PB8
microsteps: 16
# full_steps_per_rotation: 200
# rotation_distance = <full_steps_per_rotation> * <microsteps> / <steps_per_mm>
# 200 * 16 / 400 = 8
rotation_distance: 8
endstop_pin: !PA11
# Reduced by a margin of error so that the nozzle doesn't strike the bed
# when the endstop position shifts due to temperature, see note above.
# Use Z_ENDSTOP_CALIBRATE and set your own value here.
position_endstop: 0.0
position_max: 250

[extruder]
step_pin: PD6
dir_pin: PD3
enable_pin: !PB3
microsteps: 16
# full_steps_per_rotation: 200
# rotation_distance = <full_steps_per_rotation> * <microsteps> / <steps_per_mm>
# Note: the FOKOOS default Marlin e-steps are 94.0, but my testing based on
# measuring the actual length of filament fed when 100mm was requested, found 96.9
# to be more accurate.
# 200 * 16 / 96.9 = 33.02
rotation_distance: 33.02
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
# Tuned at 200C.
pid_Kp: 35.789
pid_Ki: 2.339
pid_Kd: 136.892
min_temp: 5
max_temp: 275

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
# Tuned at 60C.
pid_kp: 58.214
pid_ki: 1.075
pid_kd: 788.077

min_temp: 5
max_temp: 150

[fan]
pin: PB1

[filament_switch_sensor filament_sensor]
switch_pin: !PA4

# BLtouch configuration. Uncomment these blocks if you have installed a
# BLtouch. The correct wiring is to use the Robin Nano's BLtouch port for the
# 3-wire connector (make sure you connect it the right way round) and the Z+
# endstop port for the 2-wire connector (use the innermost 2 pins, center is
# ground). Follow Klipper's instructions for installing and calibrating the
# BLtouch to set the correct x_offset, y_offset, and z_offset values below.
# There are headers available for the BLtouch on the right hand side of the
# toolhead, behind the fan cover. Check the connections carefully with a
# multimeter before using them. The wiring can be done through the ribbon
# cables, by connecting a BLtouch extension to the PCB attached to the
# left-hand Z stepper motor, but this requires either soldering directly to
# the PCB (likely easiest), or crimping extra pins into the connector to be
# found there.
#
#[bltouch]
#sensor_pin: ^PC4
#control_pin: PA8
#x_offset: 57
#y_offset: -18
#z_offset: 2.5

# Optional bed mesh configuration you can use if you have a BLtouch installed.
#[bed_mesh]
#mesh_min: 70, 15
#mesh_max: 220, 210
#horizontal_move_z: 5
#speed: 150
