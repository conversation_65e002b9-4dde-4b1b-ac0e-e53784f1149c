# This file contains common pin mappings for the GTM32 PRO board in
# the Geeetech 301 printer. To use this config, the firmware should be
# compiled for the STM32F103 with "No bootloader", serial (on USART1
# PA10/PA9) communication and GPIO pins to set at micro-controller
# startup set to "!PB4,!PB5,!PB0,!PB1"

# The "make flash" command does not work on the Geeetech 301. Instead,
# after running "make", run the following command to flash the board:
#  stm32flash -w out/klipper.bin -v -i rts,-dtr,dtr -b 115200 /dev/ttyUSB0

# See docs/Config_Reference.md for a description of parameters.

[multi_pin heater]
pins: PB4,PB5,PB0

[multi_pin extruder_fans]
pins: PB7,PB8,PB9

[thermistor bed_thermistor]
temperature1: 24
resistance1: 104600
temperature2: 40
resistance2: 47700
temperature3: 67
resistance3: 13000

[stepper_a]
step_pin: PC6
dir_pin: PD13
enable_pin: !PA8
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE4
homing_speed: 50
position_endstop: 216
arm_length: 201

[stepper_b]
step_pin: PA12
dir_pin: PA11
enable_pin: !PA15
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE2

[stepper_c]
step_pin: PD6
dir_pin: PD3
enable_pin: !PB3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE0

[extruder]
step_pin: PC14
dir_pin: !PC13
enable_pin: !PC15
microsteps: 16
rotation_distance: 32
nozzle_diameter: 0.4
filament_diameter: 1.75
heater_pin: multi_pin:heater
sensor_type: EPCOS 100K B57560G104F
pullup_resistor: 4700
inline_resistor: 220
sensor_pin: PC0
min_temp: 0
max_temp: 250
control: pid
pid_Kp: 39
pid_Ki: 2
pid_Kd: 210

[extruder_stepper e1]
extruder:
step_pin: PA0
dir_pin: !PB6
enable_pin: !PA1
microsteps: 16
rotation_distance: 32

[extruder_stepper e2]
extruder:
step_pin: PB2
dir_pin: !PB11
enable_pin: !PC4
microsteps: 16
rotation_distance: 32

[heater_bed]
heater_pin: PB1
sensor_type: bed_thermistor
sensor_pin: PC3
min_temp: 0
max_temp: 150
control: pid
pid_Kp: 67
pid_Ki: 4
pid_Kd: 310

[temperature_sensor board]
sensor_type: temperature_mcu
gcode_id: MCU

[temperature_sensor secondary]
sensor_pin: PC1
sensor_type: EPCOS 100K B57560G104F
pullup_resistor: 4700
inline_resistor: 220
gcode_id: SEC

[temperature_sensor ambient]
sensor_pin: PC2
sensor_type: EPCOS 100K B57560G104F
pullup_resistor: 4700
inline_resistor: 220
gcode_id: AMB

[homing_heaters]
heaters: extruder

[heater_fan extruder]
pin: multi_pin:extruder_fans
heater: extruder
max_power: 0.8
off_below: 0.2
shutdown_speed: 0

[mcu]
serial: /dev/ttyUSB0
restart_method: cheetah

[printer]
kinematics: delta
max_velocity: 300
max_accel: 3000
max_z_velocity: 150
delta_radius: 94

[output_pin beep]
pin: PB10

[output_pin lcd_beep]
pin: PE12

[display]
lcd_type: hd44780
rs_pin: PE6
e_pin: PE14
d4_pin: PD8
d5_pin: PD9
d6_pin: PD10
d7_pin: PE15
encoder_pins: ^PE9,^PE8
click_pin: ^PE13
