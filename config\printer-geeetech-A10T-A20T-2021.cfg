# This file contains common pin mappings for the Geeetech GT2560 v4.0 and v4.1b
# boards. These boards use a firmware compiled for the AVR atmega2560.
# For default Geeetech A10/A20  (1 extruder),
#                      A10M/A20M (mixing 2 in 1 out),
#                      A10T/A20T (mixing 3 in 1 out) printers
# Installation: https://www.klipper3d.org/Installation.html
# Always read for first start: https://www.klipper3d.org/Config_checks.html

[mcu]
# Might need to be changed: https://www.klipper3d.org/Installation.html
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0

[printer]
kinematics: cartesian
max_velocity: 200
max_accel: 1500
max_z_velocity: 20
max_z_accel: 500

# # uncomment for BLTouch/3DTouch
# [bltouch]
# sensor_pin: PC7 # there is an external pull up so no need in ^
# control_pin: PB5
# speed: 3.0
# samples: 2
# x_offset: -42.0
# y_offset: -1.0
#                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      z_offset: 1.0 # during calibration this line is commented out and new record added at the end of file

[safe_z_home]
home_xy_position: 100, 100  # Change coordinates to the center of your print bed
speed: 50
z_hop: 10  # Move up 10mm
z_hop_speed: 5

[stepper_x]
enable_pin: !PC2
dir_pin: !PG2
step_pin: PC0
microsteps: 16
rotation_distance: 40
endstop_pin: !PA2 # there are external pull ups
position_endstop: 0
position_max: 220 # for A10/M/T / change to 250 for A20/M/T
homing_speed: 40

[stepper_y]
enable_pin: !PA7
dir_pin: !PC4
step_pin: PC6
microsteps: 16
rotation_distance: 40
endstop_pin: !PA6 # there are external pull ups
position_endstop: 0
position_max: 220 # for A10/M/T / change to 250 for A20/M/T
homing_speed: 40

[stepper_z]
enable_pin: !PA5
dir_pin: PA1
step_pin: PA3
microsteps: 16
rotation_distance: 8
#endstop_pin: probe:z_virtual_endstop # uncomment for BLTouch/3DTouch
endstop_pin: !PC7 # comment for BLTouch/3DTouch
position_endstop: 0 # comment for BLTouch/3DTouch
position_max: 230 # for A10/M/T / change to 250 for A20/M/T
position_min: -5
homing_speed: 20

[extruder]
enable_pin: !PB6
dir_pin: PL5
step_pin: PL3
microsteps: 16
rotation_distance: 8 # Needs to be optimized: https://www.klipper3d.org/Rotation_Distance.html#calibrating-rotation_distance-on-extruders
nozzle_diameter: 0.4
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK3
min_temp: 0
max_temp: 250
max_extrude_only_distance: 200.0
# Parameters for stock hotend on A10M
# Please recalibrate according to https://www.klipper3d.org/Config_checks.html#calibrate-pid-settings
control: pid
pid_kp: 54.722
pid_ki: 4.800
pid_kd: 155.958

[extruder_stepper extruder_1]
extruder:
enable_pin: !PL1
dir_pin: PL2
step_pin: PL0
microsteps: 16
rotation_distance: 8 # Needs to be optimized: https://www.klipper3d.org/Rotation_Distance.html#calibrating-rotation_distance-on-extruders

[extruder_stepper extruder_2]
extruder:
enable_pin: !PG0
dir_pin: PL4
step_pin: PL6
microsteps: 16
rotation_distance: 8 # Needs to be optimized: https://www.klipper3d.org/Rotation_Distance.html#calibrating-rotation_distance-on-extruders

[heater_bed]
heater_pin: PG5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK2
min_temp: 0
max_temp: 120
# Parameters for `SuperPlate` on A10M
# Please recalibrate according to https://www.klipper3d.org/Config_checks.html#calibrate-pid-settings
control: pid
pid_kp: 70.936
pid_ki: 1.785
pid_kd: 704.924

[fan]
pin: PH6
cycle_time: 0.150
kick_start_time: 0.300

# # for GT2560V4.0 with 20pin flat cable toward the display
# [display]
# lcd_type: hd44780
# hd44780_protocol_init: True
# rs_pin: PD1
# e_pin: PH0
# d4_pin: PH1
# d5_pin: PD0
# d6_pin: PE3
# d7_pin: PC1
# encoder_pins: ^PG1, ^PL7
# click_pin: ^!PD2


# for GT2560V4.1B with 12pin flat cable toward the display YHCB2004-06 ver3.0
# the aip31068_spi driver was added to Klipper on 2024-12-02, commit aecb29d2
[display]
lcd_type: aip31068_spi
latch_pin: PE3
spi_software_sclk_pin: PD0
spi_software_mosi_pin: PC1
spi_software_miso_pin: PH7 # any unused pin
encoder_pins: ^PH0, ^PH1
click_pin: ^!PD2


[filament_switch_sensor sensor_e0]
switch_pin: !PK4

[filament_switch_sensor sensor_e1]
switch_pin: !PK5

[filament_switch_sensor sensor_e2]
# switch_pin: !PE2 # for GT2560V4.0
switch_pin: !PF0 # for GT2560V4.1B

# to enable M118 echo command
[respond]

# Specific macros for mixing colors.
# Add in slicer new filament color and in filament start G-Code add desired mixing factor:
#     M163 S0 P50 ; set extruder 0 to 50%
#     M163 S1 P40 ; set extruder 1 to 40%
#     M163 S2 P10 ; set extruder 2 to 10%
#     M164 ; commit the mix factors
[gcode_macro M163]
description:  M163 [P<factor>] [S<index>] Set a single mix factor (in proportion to the sum total of all mix factors). The mix must be committed to a virtual tool by M164 before it takes effect.
gcode:
    {% if 'P' in params %}
            {% set s = params.S|default(0)| int %}
            {% if s == 0 %}
                SET_GCODE_VARIABLE MACRO=M164 VARIABLE=e0_parts VALUE={params.P|default(0)|float}
                M118 Set Mixing factor for extruder 0 to {params.P|default(0)|float}
            {% elif s == 1 %}
                SET_GCODE_VARIABLE MACRO=M164 VARIABLE=e1_parts VALUE={params.P|default(0)|float}
                M118 Set Mixing factor for extruder 1 to {params.P|default(0)|float}
            {% elif s == 2 %}
                SET_GCODE_VARIABLE MACRO=M164 VARIABLE=e2_parts VALUE={params.P|default(0)|float}
                M118 Set Mixing factor for extruder 2 to {params.P|default(0)|float}
            {% endif %}
    {% else %}
        M118 No Mixing factor set, missing value for P
    {% endif %}
    M118 {e0_parts} {e1_parts} {e2_parts}


[gcode_macro M164]
description: Applies the set mixing factors to the extruders
# default values:
variable_e0_parts : 100
variable_e1_parts : 0
variable_e2_parts : 0
gcode:
    # normalize the parts to sum of 1
    {% set e0 = e0_parts / (e0_parts + e1_parts + e2_parts) | float %}
    {% set e1 = e1_parts / (e0_parts + e1_parts + e2_parts) | float %}
    {% set e2 = e2_parts / (e0_parts + e1_parts + e2_parts) | float %}
    M118 scaled rot-dist_e0 { printer.configfile.settings.extruder.rotation_distance / (e0 + 0.000001) | float }
    M118 scaled rot-dist_e1 { printer.configfile.settings['extruder_stepper extruder_1'].rotation_distance / (e1 + 0.000001) | float }
    M118 scaled rot-dist_e2 { printer.configfile.settings['extruder_stepper extruder_2'].rotation_distance / (e2 + 0.000001) |float }
    # activate stepper percentages
    SYNC_EXTRUDER_MOTION EXTRUDER=extruder MOTION_QUEUE=extruder
    SYNC_EXTRUDER_MOTION EXTRUDER=extruder_1 MOTION_QUEUE=extruder
    SYNC_EXTRUDER_MOTION EXTRUDER=extruder_2 MOTION_QUEUE=extruder
    SET_EXTRUDER_ROTATION_DISTANCE EXTRUDER=extruder DISTANCE={ printer.configfile.settings.extruder.rotation_distance / (e0+0.000001)|float }
    SET_EXTRUDER_ROTATION_DISTANCE EXTRUDER=extruder_1 DISTANCE={ printer.configfile.settings['extruder_stepper extruder_1'].rotation_distance / (e1+0.000001)|float }
    SET_EXTRUDER_ROTATION_DISTANCE EXTRUDER=extruder_2 DISTANCE={ printer.configfile.settings['extruder_stepper extruder_2'].rotation_distance / (e2+0.000001)|float }
    M118 Mixing factors {e0} {e1} {e2} are activated

# In PrusaSlicer:
#    - you can add as many extruders as mixing ratios you want
#    - in Printer Settings -> Custom G-code -> Tool change G-code add:
#        TOOL_CHANGE EXTRUDER={next_extruder}
#    - in this config file add:
#           [gcode_macro TOOL_CHANGE]
#           description: Tool change macro with mix ratio setup for 11 extruders
#           variable_extruder: 0
#           gcode:
#               {% set extruder = params.EXTRUDER|default(0)| int %}
#               {% if extruder == 0 %}
#                   M163 S0 P100
#                   M163 S1 P0
#                   M163 S2 P0
#                   M164
#                   M118 Switching to Extruder 0
#               {% elif extruder == 1 %}
#                   M163 S0 P90
#                   M163 S1 P10
#                   M163 S2P0
#                   M164
#                   M118 Switching to Extruder 1
#               {% elif extruder == 2 %}
#                   # and so on ...
#               {% else %}
#                   M118 Unknown extruder number: {extruder}
#               {% endif %}

# In OrcaSlicer:
# you can add as many filaments as mixing ratios you want
# in Material settings -> Advanced -> Filament start G-code add desired mixing ratio:
#    ; filament start gcode
#    M163 S0 P100 ; set extruder 0
#    M163 S1 P0 ; set extruder 1
#    M163 S2 P0 ; set extruder 2
#    M164 ; commit the mix factors

# For gradient over Z axis:
# In `Printer -> Custom G-code -> After layer change G-code` add:
#     M163 S0 P{ layer_num * 100 / total_layer_count } ; Gradient 0-100
#     M163 S1 P{(total_layer_count-layer_num) * 100 / total_layer_count} ; Gradient 100-0
#     M164 ; commit the mix factors
