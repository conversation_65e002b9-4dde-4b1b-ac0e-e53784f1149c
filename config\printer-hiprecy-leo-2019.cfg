# This file contains pin mappings for the stock Hiprecy Leo (based on Fysetc F6 board) with stock TMC2130 SPI drivers.
# The Self-levelling plate sensor is PINDA probe type

################################################################################
# CAUTION: The Touchscreen is a DWIN T5UID1 and cannot be used with Klipper!!! #
# You will lose all the display functionalities!!!                             #
################################################################################

# To use this config, the firmware should be compiled for the AVR atmega2560 (16MHz).

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: tmc2130_stepper_x:virtual_endstop
homing_retract_dist: 0
homing_speed: 50
position_endstop: 0
position_max: 220

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: tmc2130_stepper_y:virtual_endstop
homing_retract_dist: 0
homing_speed: 50
position_endstop: 0
position_max: 230

[stepper_z]
step_pin: PL6
dir_pin: !PL1
enable_pin: !PF4
microsteps: 16
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
position_min: -2
position_max: 260

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 23.133 ### Based on default 415 steps/mm. See https://www.klipper3d.org/Rotation_Distance.html for fine tuning
gear_ratio: 50:17
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PE3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK4
max_extrude_only_distance: 400
control: pid
pid_Kp: 22
pid_Ki: 1.08
pid_Kd: 114
min_extrude_temp: 180
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK7
control: pid
pid_kp: 69.584
pid_ki: 1.393
pid_kd: 868.925
min_temp: 0
max_temp: 130

########################################
# Probe configuration
########################################

[probe]
pin: PB6 # Z endstop pin
x_offset: 35
y_offset: 2
z_offset: 0.5
samples: 2
speed: 5

########################################
# Bed Mesh example
########################################

[bed_mesh]
mesh_min: 40, 40
mesh_max: 185, 230
probe_count: 4, 4
speed: 100
fade_end: 0.0
algorithm: bicubic

###############################################
# Homing Override example for sensorless homing
###############################################
# Temporarily reduces stepper X and Y current for sensorless homing
# then resets the current to the previous value (default value)

[homing_override]
gcode:
    G90
    G1 Z5
    SET_TMC_CURRENT STEPPER=stepper_x CURRENT=0.500
    G28 X ;Zero X
    SET_TMC_CURRENT STEPPER=stepper_x CURRENT=0.600
    G1 X10 ;Move X 10mm away from the stop so we can home multiple times in a row (needs a bit of space to trigger reliably again)
    SET_TMC_CURRENT STEPPER=stepper_y CURRENT=0.500
    G28 Y ;Zero Y
    SET_TMC_CURRENT STEPPER=stepper_y CURRENT=0.700
    G1 Y10 ;Move Y 10mm away from the stop so we can home multiple times in a row (needs a bit of space to trigger reliably again)
    G28 Z
    G1 X20 Y5
set_position_z: 0

#fan for printed model FAN0
[fan]
pin: PL5

#fan for hotend FAN1
[heater_fan e0_fan]
pin: PL4
heater: extruder
max_power: 1.0
shutdown_speed: 0
cycle_time: 0.010
kick_start_time: 0.100
heater_temp: 50.0
fan_speed: 1.0

#fan for control board FAN2
[heater_fan board_fan]
pin: PL3

#Prevents communication issues with SPI drivers
[static_digital_output disable_sdcard]
pins: PB0

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 8
max_z_accel: 100

########################################
# TMC2130 SPI configuration
########################################

[tmc2130 stepper_x]
cs_pin: PG4
diag1_pin: ^!PK1
run_current: 0.600
stealthchop_threshold: 999999
driver_SGT: 3

[tmc2130 stepper_y]
cs_pin: PG2
diag1_pin: ^!PJ1
run_current: 0.700
stealthchop_threshold: 999999
driver_SGT: 3

[tmc2130 stepper_z]
cs_pin: PJ7
run_current: 0.800
stealthchop_threshold: 999999

[tmc2130 extruder]
cs_pin: PL2
diag1_pin: PE4
run_current: 0.600
stealthchop_threshold: 999999
