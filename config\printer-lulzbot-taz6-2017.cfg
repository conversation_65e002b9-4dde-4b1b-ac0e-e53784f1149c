# This file contains pin mappings for the Lulzbot TAZ 6 circa 2017. To
# use this config, the firmware should be compiled for the AVR
# atmega2560.

# For a TAZ6 with DualV3 extruder, see printer-lulzbot-taz6-dual-v3-2017.cfg.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC0
dir_pin: PL1
enable_pin: !PA7
microsteps: 16
rotation_distance: 32
endstop_pin: ^PB6
position_endstop: -20
position_min: -20
position_max: 300
homing_speed: 50

[stepper_y]
step_pin: PC1
dir_pin: !PL0
enable_pin: !PA6
microsteps: 16
rotation_distance: 32
endstop_pin: ^PA1
position_endstop: 306
position_min: -20
position_max: 306
homing_speed: 50

[stepper_z]
step_pin: PC2
dir_pin: PL2
enable_pin: !PA5
microsteps: 16
rotation_distance: 2
endstop_pin: ^!PB4
position_endstop: -0.7
position_min: -1.5
position_max: 270
homing_speed: 1

# settings for "Tilapia" Hexagon extruder (TAZ6 standard)
[extruder]
step_pin: PC3
dir_pin: !PL6
enable_pin: !PA4
microsteps: 16
gear_ratio: 48:9
rotation_distance: 20.562
nozzle_diameter: 0.400
filament_diameter: 2.920
heater_pin: PH6
sensor_type: ATC Semitec 104GT-2
sensor_pin: PF0
control: pid
pid_Kp: 28.79
pid_Ki: 1.91
pid_Kd: 108.51
min_temp: 0
max_temp: 300
min_extrude_temp: 140

# settings for "Angelfish" Aerostruder (E3D Titan Aero V6)
#[extruder]
#step_pin: PC3
#dir_pin: !PL6
#enable_pin: !PA4
#microsteps: 16
#rotation_distance: 7.619
#nozzle_diameter: 0.400
#filament_diameter: 2.920
#heater_pin: PH6
#sensor_type: ATC Semitec 104GT-2
#sensor_pin: PF0
#control: pid
#pid_Kp: 21.00
#pid_Ki: 1.78
#pid_Kd: 61.93
#min_temp: 0
#max_temp: 300
#min_extrude_temp: 140

[heater_bed]
heater_pin: PE5
sensor_type: Honeywell 100K 135-104LAG-J01
sensor_pin: PF2
control: pid
pid_Kp: 162.0
pid_Ki: 17.0
pid_Kd: 378.0
min_temp: 0
max_temp: 130

[fan]
pin: PH5

[heater_fan heatbreak_cooling_fan]
pin: PH3

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 2
max_z_accel: 10

[ad5206 stepper_digipot]
enable_pin: PD7
scale: 2.08
# Channel 1 is E0, 2 is E1, 3 is unused, 4 is Z, 5 is X, 6 is Y
channel_1: 1.34
channel_2: 1.0
channel_4: 1.1
channel_5: 1.1
channel_6: 1.1

# Enable 16 micro-steps on steppers X, Y, Z, E0, E1
[static_digital_output stepper_config]
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4,
    PK1, PK2

[static_digital_output yellow_led]
pins: !PB7

[display]
lcd_type: st7920
cs_pin: PG4
sclk_pin: PJ2
sid_pin: PG3
encoder_pins: ^PJ6,^PJ5
click_pin: ^!PE2
menu_timeout: 5

[probe]
pin: ^!PA0
z_offset:1.377
speed: 0.5
lift_speed: 1.5
samples: 2
sample_retract_dist: 1.0
samples_tolerance: 0.075

[bed_tilt]
points: -9, -9
        289.4, -9
        289.4, 290
        -9, 289
speed: 75
horizontal_move_z: 5

[safe_z_home]
home_xy_position: -19.1, 259.3
speed: 50.0
z_hop: 10.0

# Perform ABL by running G29 in START gcode
[gcode_macro G29]
gcode:
    BED_TILT_CALIBRATE
