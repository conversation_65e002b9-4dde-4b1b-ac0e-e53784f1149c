# Support for Makergear M2 printers circa 2012 that have the RAMBo
# v1.0d electronics along with the V3A extruder.  The electronics use
# Allegro A4984 stepper drivers with 1/8th micro-stepping.  To use
# this config, the firmware should be compiled for the AVR atmega2560.

[stepper_x]
step_pin: PC0
dir_pin: !PL1
enable_pin: !PA7
microsteps: 8
rotation_distance: 36
endstop_pin: ^!PB6
position_endstop: 0.0
position_max: 200
homing_speed: 50

[endstop_phase stepper_x]
endstop_accuracy: .200

[stepper_y]
step_pin: PC1
dir_pin: PL0
enable_pin: !PA6
microsteps: 8
rotation_distance: 36
endstop_pin: ^!PB5
position_endstop: 0.0
position_max: 250
homing_speed: 50

[endstop_phase stepper_y]
endstop_accuracy: .200

[stepper_z]
step_pin: PC2
dir_pin: !PL2
enable_pin: !PA5
microsteps: 8
rotation_distance: 8
endstop_pin: ^!PB4
position_min: 0.1
position_endstop: 0.7
position_max: 200
homing_retract_dist: 2.0

[endstop_phase stepper_z]
endstop_accuracy: .070

[extruder]
step_pin: PC3
dir_pin: PL6
enable_pin: !PA4
microsteps: 8
gear_ratio: 57:11
rotation_distance: 35.170
nozzle_diameter: 0.350
filament_diameter: 1.750
heater_pin: PH6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF0
control: pid
pid_Kp: 7.0
pid_Ki: 0.1
pid_Kd: 12
min_temp: 0
max_temp: 210

[heater_bed]
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF2
control: watermark
min_temp: 0
max_temp: 100

[fan]
pin: PH5

[heater_fan nozzle_fan]
pin: PH3
max_power: 0.61
cycle_time: .000030
hardware_pwm: True

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 3000
max_z_velocity: 25
max_z_accel: 30

[ad5206 stepper_digipot]
enable_pin: PD7
# Scale the config so that the channel value can be specified in amps
scale: 1.56
# Channel 1 is E0, 2 is E1, 3 is unused, 4 is Z, 5 is X, 6 is Y
channel_1: 1.0
channel_2: 0.75
channel_4: 0.62 # Z-motor with 30 Ohm coils and 19V power can't exceed .633 Amps
channel_5: 0.82
channel_6: 0.82

# Enable 8 micro-steps on steppers X, Y, Z, E0
[static_digital_output stepper_config]
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4

[static_digital_output yellow_led]
pins: !PB7
