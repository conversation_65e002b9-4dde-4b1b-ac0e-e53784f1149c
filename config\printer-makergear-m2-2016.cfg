# Support for Makergear M2 printers circa 2016 that have the RAMBo
# electronics along with the V4 extruder and the black and silver Z
# stepper.  The electronics use Allegro A4984 stepper drivers with
# 1/16th micro-stepping.  To use this config, the firmware should be
# compiled for the AVR atmega2560.

[stepper_x]
step_pin: PC0
dir_pin: !PL1
enable_pin: !PA7
microsteps: 16
rotation_distance: 36
endstop_pin: ^!PB6
position_endstop: 0
position_max: 205
homing_speed: 50

[stepper_y]
step_pin: PC1
dir_pin: PL0
enable_pin: !PA6
microsteps: 16
rotation_distance: 36
endstop_pin: ^!PB5
position_endstop: 0
position_max: 250
homing_speed: 50

[stepper_z]
step_pin: PC2
dir_pin: !PL2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PB4
position_min: 0.1
position_endstop: 0.7
position_max: 200
homing_speed: 15
homing_retract_dist: 2.0

[extruder]
step_pin: PC3
dir_pin: PL6
enable_pin: !PA4
microsteps: 16
gear_ratio: 57:11
rotation_distance: 31.174
nozzle_diameter: 0.350
filament_diameter: 1.750
heater_pin: PH6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF0
control: pid
pid_kp: 26.137
pid_ki: 1.489
pid_kd: 114.675
min_temp: 0
max_temp: 275
max_extrude_only_distance: 150.0

[heater_bed]
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF2
control: watermark
min_temp: 0
max_temp: 90

[fan]
pin: PH5

[heater_fan heatbreak_cooling_fan]
pin: PH3

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 3000
max_z_velocity: 25
max_z_accel: 100

[ad5206 stepper_digipot]
enable_pin: PD7
# Scale the config so that the channel value can be specified in amps.
scale: 2.08
# Channel 1 is E0, 2 is E1, 3 is unused, 4 is Z, 5 is X, 6 is Y
channel_1: 1.25
channel_2: 0.75
channel_4: 0.75
channel_5: 0.75
channel_6: 0.75

# Enable 16 micro-steps on steppers X, Y, Z, E0, E1
[static_digital_output stepper_config]
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4,
    PK1, PK2

[static_digital_output yellow_led]
pins: !PB7
