# This file contains common pin mappings for the 2017 Monoprice
# Mini Delta. To use this config, the firmware should be compiled for the
# STM32F070 microcontroller with an 8MHz crystal and USB for communication.
#
# IMPORTANT: Use of Kipper with a Monoprice Mini Delta requires a power supply
# with a _minimum_ 120 W (i.e. 10 A) capacity! The stock power supply will not
# work, as it can't power the hotend and the bed at the same time.
#
# Note 1: Some controller boards only trigger the bootloader when an update file
# is "printed" from the LCD, rather than supporting bootloader triggering
# during bootup. It may be preferable to use an ST-Link V2 to flash the
# firmware instead. Follow this link for an example of the commands needed to
# do this: https://github.com/arkorobotics/MiniDeltaBootloader
#
# Note 2: Klipper does not currently support the LCD interface used on this
# printer.
#
# See docs/Config_Reference.md for a description of parameters.


# Stepper A is the front-left tower, as originally wired. If you've rewired
# your steppers to "correct" this, you'll need to swap these values.
[stepper_a]
homing_speed: 50
step_pin: PB12
dir_pin: PB11
enable_pin: !PB10
microsteps: 16
rotation_distance: 56
endstop_pin: ^PC14
position_endstop: 125.00
arm_length: 120.8
# Some users report better success using the following value for arm_length:
#arm_length: 121.36

[stepper_b]
step_pin: PB2
dir_pin: PB1
enable_pin: !PB10
microsteps: 16
rotation_distance: 56
endstop_pin: ^PC15

[stepper_c]
step_pin: PB14
dir_pin: PB13
enable_pin: !PB10
microsteps: 16
rotation_distance: 56
endstop_pin: ^PC13

[extruder]
step_pin: PA7
dir_pin: !PA6
enable_pin: !PB0
microsteps: 16
rotation_distance: 65.984
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
# calibrated at 240C on stock hardware
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 300

[heater_bed]
heater_pin: PA5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA4
# calibrated at 70C on stock hardware
control: pid
pid_kp: 51.249
pid_ki: 4.437
pid_kd: 147.983
min_temp: 0
max_temp: 130

# Print cooling fan (omit section if fan not present)
[fan]
pin: PA8

[mcu]
serial: /dev/ttyACM0
restart_method: command

[printer]
kinematics: delta
max_velocity: 150
max_accel: 1200
max_z_velocity: 100
# minimum_z_position is negative to allow delta calibration to work
minimum_z_position: -10.0
delta_radius: 63.00
# Some users report better success using the following value for arm_length:
#delta_radius: 62.70

[delta_calibrate]
radius: 50
speed: 50
horizontal_move_z: 5

######################################################################
# Bed leveling support
######################################################################

[bed_mesh]
speed: 50
horizontal_move_z: 5
mesh_radius: 50
round_probe_count: 5
fade_start: 1.0
fade_end: 0.0

######################################################################
# Bed probing hardware
######################################################################

[probe]
pin: ^!PB7
x_offset: 0.0
y_offset: 0.0
z_offset: -0.1
speed: 5.0
samples: 1
sample_retract_dist: 2.0
samples_result: average
samples_tolerance: 0.100
samples_tolerance_retries: 0

######################################################################
# G-Code macros and events
######################################################################

# G-Code macros (one may define any number of sections with a
# "gcode_macro" prefix).
[gcode_macro START_PRINT]
gcode:
    G90 ; switch to absolute positioning - this line was added in case Cura doesn't include it by default
    M82 ; set extruder to absolute mode - this line was added in case Cura doesn't include it by default
    G28 ; start from home position
    BED_MESH_CALIBRATE
    G1 Z15.0 F4800  ; move the nozzle up 15mm
    G92 E0 ; reset extrusion distance
    G1 F200 E5 ; extrude 3mm of feed stock
    G92 E0 ; reset extrusion distance again
    G1 F4800

[gcode_macro END_PRINT]
gcode:
    M104 S0 ; turn off hotend heater
    M140 S0 ; turn off bed heater
    G91 ; Switch to use Relative Coordinates
    G1 E-3 F700 ; retract the filament a bit before lifting the nozzle to release some of the pressure
    G1 Z2 F1000 ; raise platform 2mm from current position
    G1 E-2 F700 ; retract filament even more
    G90 ; Switch back to using absolute coordinates
    G28 X Y Z ; Return to home position. Without an X Y or Z after G28 the print completion time will not be displayed on the Mini Delta's display.
    M84 ; disable motors
    M107 ; turn off fan. Don't use M106 S1. Using M106 S1 may prevent the fan from turning on for the following print. Some say M107 doesn't work for them but if the hotend is below 70 degrees it should work
