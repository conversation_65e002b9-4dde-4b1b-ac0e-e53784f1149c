# This file contains common pin mappings for the Monoprice
# Select Mini v1. To use this config, the firmware should be compiled for the
# STM32F103 microcontroller with an 8MHz crystal and USB for communication.
#
# Use the following settings in make menuconfig:
#
#  * Enable extra low-level configuration options: Enable
#  * Microcontroller architecture: STM32
#  * Processor: STM32F103
#  * Bootloader offset: 8KiB bootloader
#  * Clockspeed: 8 Mhz crystal
#  * Communication interface: USB (on PA11/PA12)
#  * GPIO pins to set: PA8, PB1, PB11, PB9
#
#  IMPORTANT: A bootloader offset of 8KiB will preserver the stock bootloader
#  and allows easy flashing via SDCard without additional hardware.
#
#  IMPORTANT: Setting PB9 in the the GPIO pins to set at micro-controller
#  startup is required for the USB port to function.
#
#  PA8, PB1, PB11 in the GPIO pins deactivates the steppers until klippy
#  takes over.
#
# Note 1: Prior to flashing klipper, if possible, make a copy of the default
# or current configuration values for the printer.  Running an M503 command
# will output these values.
#
# Note 2: Klipper can be flashed by copying out/klipper.bin to the MPSMv1's
# SDCard, renaming it to update.bin, and creating an empty file on the SD card
# named fcupdate.flg.  Insert the SDCard while the printer is off it on.  After
# klipper is flashed, remove the SDCard and delete update.bin and fcupdate.flg
# to prevent the firmware from flashing on every subsequent power-on.
# Returning to stock firmware without a programmer is possible as long as the
# bootloader is not overwritten.
#
# Note 3: Stepper directions are not consistent in MSPMv1 printers. Check the
# directions of the printer with M503 in the stock firmware and adjust the
# dir_pins below accordingly.
#
# Note 4: Klipper currently does not support the LCD-UI of this
# printer, which is connected via serial interface and controlled by an
# esp8266 using a custom protocol.
#
# See ../docs/Config_Reference.md file for a description of all parameters.

[stepper_x]
homing_speed: 15
step_pin: PB14
dir_pin: !PB15 # modify stepper direction if necessary
enable_pin: !PA8
# rotation_distance varies in the printer model. Check the correct
# step-rate of the Select Mini in the original firmware (with M503)
# and calculate the appropriate value for rotation_distance. This has
# to be done for all axes.
microsteps: 16
rotation_distance: 34.510 # 17 teeth on pulley; MXL belt (2.03 pitch)
endstop_pin: ^!PB4
position_endstop: 0
position_max: 120 # default bed width
position_min: 0

[stepper_y]
homing_speed: 15
step_pin: PB12
dir_pin: PB13 # modify stepper direction if necessary
enable_pin: !PA8
microsteps: 16
rotation_distance: 34.510 # check comment in [stepper_x] section
endstop_pin: ^!PA15
position_endstop: 0
position_max: 120 # default bed length
position_min: 0

[stepper_z]
homing_speed: 10
step_pin: PB10
dir_pin: PB2 # modify stepper direction if necessary
enable_pin: !PB11
microsteps: 16
full_steps_per_rotation: 48
rotation_distance: 0.7 # M4 rod.  check comment in [stepper_x] section
endstop_pin: ^!PB5
position_endstop: 0.5
position_max: 120 # default height

[extruder]
# extruder stepper
step_pin: PB0
dir_pin: !PC13 # modify stepper direction if necessary
enable_pin: !PB1
microsteps: 16
rotation_distance: 32.990 # 97 steps/mm. check comment in [stepper_x] section
nozzle_diameter: 0.400
filament_diameter: 1.750
# heater
heater_pin: PB6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_kp: 20.00
pid_ki: 0.02
pid_kd: 250.00
# temperatures
min_temp: 0
max_temp: 250
max_extrude_only_distance: 425 #for (un-)loading

[heater_bed]
heater_pin: PB7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA1
control: pid
pid_kp: 70.00
pid_ki: 1.50
pid_kd: 812.00
min_temp: 0
max_temp: 85

# Print cooling fan
[heater_fan hotend_fan]
pin: PB8
heater: extruder
heater_temp: 45.0

[mcu]
serial: /dev/ttyACM0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 150
max_accel: 800
max_z_velocity: 1.5
max_z_accel: 20

# Positions for BED_SCREWS_ADJUST levelling with bed screws still accessible
[bed_screws]
screw1: 22, 10
screw2: 22, 90
screw3: 98, 90
screw4: 98, 10
