# This file contains pin mappings for a full Ratrig V-Minion kit
# with an Octopus Pro v1.1 board.
#
# This will not work with RatOS
#
# To use this config, during "make menuconfig" select the STM32F446
# with a "32KiB bootloader", USB (on PA11/PA12) communication, and
# a "12MHZ Crystal"
#
# Flash this firmware on the MCU by copying "out/klipper.bin" to an SD
# card and turning the printer on with the card inserted. The firmware
# filename must be named "firmware.bin"
#
# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF13
dir_pin: !PF12
enable_pin: !PF14
endstop_pin: ^PG6
rotation_distance: 40
microsteps: 64
position_max: 180
position_min: 0
homing_speed: 60
position_endstop: 0

[tmc2209 stepper_x]
uart_pin: PC4
run_current: 0.8
interpolate: false
stealthchop_threshold: 0

[stepper_y]
step_pin: PG0
dir_pin: PG1
enable_pin: !PF15
endstop_pin: ^PG9
rotation_distance: 40
microsteps: 64
position_max: 180
position_min: 0
homing_speed: 60
position_endstop: 0

[tmc2209 stepper_y]
uart_pin: PD11
run_current: 0.8
interpolate: false
stealthchop_threshold: 0

[stepper_z]
step_pin: PC13
dir_pin: !PF0
enable_pin: !PF1
endstop_pin: probe:z_virtual_endstop
rotation_distance: 4
position_min: -5
microsteps: 64
position_max: 180

[tmc2209 stepper_z]
uart_pin: PE4
run_current: 0.8
interpolate: false
stealthchop_threshold: 0

[extruder]
rotation_distance: 5.57
full_steps_per_rotation: 200
filament_diameter: 1.750
step_pin: PF11
dir_pin: !PG3
enable_pin: !PG5
microsteps: 64
nozzle_diameter: 0.4
heater_pin: PA2
sensor_type: Generic 3950
sensor_pin: PF4
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114

min_temp: 0
max_temp: 290

[tmc2209 extruder]
uart_pin: PC6
run_current: 0.70
stealthchop_threshold: 0
interpolate: False

[probe]
pin: ^PB7
x_offset: -24.0
y_offset: -13.0
z_offset: 0.0
speed: 5
samples: 2
sample_retract_dist: 2
lift_speed: 5.0
samples_result: median
samples_tolerance: 0.02
samples_tolerance_retries: 5

[bed_mesh]
speed: 300
horizontal_move_z: 5
mesh_min: 15,15
mesh_max: 150,160
probe_count: 5,5
fade_start: 1.0
fade_end: 10.0
mesh_pps: 2,2
algorithm: bicubic
bicubic_tension: .2

[screws_tilt_adjust]
screw1: 80, 108
screw1_name: Left Screw
screw2: 155, 72
screw2_name: Front Right Screw
screw3: 155, 147
screw3_name: Rear Right Screw
horizontal_move_z: 10
speed: 300
screw_thread: CCW-M4

[safe_z_home]
home_xy_position: 90,90
z_hop: 5
speed: 300

[heater_bed]
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF3
control: pid
pid_kp: 54.027
pid_ki: 0.770
pid_kd: 948.182
min_temp: 0
max_temp: 120

[fan]
pin: PA8
shutdown_speed: 0.0

[heater_fan hotend_cooling_fan]
pin: PE5
fan_speed: 1.0
heater: extruder
heater_temp: 50.0

[controller_fan controller_fan]
pin: PD12
fan_speed: 1.0
stepper: stepper_x, stepper_y, stepper_z

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 20000
max_z_velocity: 15
max_z_accel: 2000

[mcu]
serial: INSERTSERIALIDHERE

[board_pins octopus_11_tmc2209]
aliases:
## Expansion ports
  # EXP1 header
  EXP1_1=PE8, EXP1_3=PE9, EXP1_5=PE12, EXP1_7=PE14, EXP1_9=<GND>,
  EXP1_2=PE7, EXP1_4=PE10, EXP1_6=PE13, EXP1_8=PE15, EXP1_10=<5V>,
  # EXP2 header
  EXP2_1=PA6, EXP2_3=PB1, EXP2_5=PB2, EXP2_7=PC15,  EXP2_9=<GND>,
  EXP2_2=PA5, EXP2_4=PA4, EXP2_6=PA7, EXP2_8=<RST>, EXP2_10=PC5,
  # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi2"
