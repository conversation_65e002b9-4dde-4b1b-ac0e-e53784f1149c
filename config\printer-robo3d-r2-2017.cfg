# This file contains pin mappings for the Robo3D R2 ROBOmainboard 2.1.5.
# To use this config, the firmware should be compiled for the AVR atmega2560.
#
# See docs/Config_Reference.md for a description of parameters.
#
# The filament runout sensor is gpio21 on the RPi host. Host MCU setup is
# documented in docs/RPi_microcontroller.md.

[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE5
position_endstop: 0
position_max: 195
homing_speed: 120

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1
position_endstop: 195
position_min: 0
position_max: 195
homing_speed: 120

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 4
# Required if not using probe for the virtual endstop
# endstop_pin: ^PD3
# position_endstop: 250 # Will need adjustment
endstop_pin: probe:z_virtual_endstop
homing_speed: 10.0
position_max: 250
position_min: -10

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 21
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
min_temp: 0
max_temp: 290
control: pid
pid_kp: 27.740
pid_ki: 1.581
pid_kd: 121.707

# Not enabled in stock R2 config or supported by Robo3D
#[extruder1]
#step_pin: PC1
#dir_pin: PC3
#enable_pin: !PC7
#heater_pin: PH6
#sensor_type: EPCOS 100K B57560G104F
#sensor_pin: PK7

[probe]
pin: ^PD2
x_offset: 2
y_offset: 30
z_offset: 1 # Safe value. Adjust for your nozzle offset.
sample_retract_dist: 1.0
# Below are very conservative settings, as the IR probe has repeatability
# issues depending on visual characteristics of the build surface.
# Polycarbonate build surfaces (e.g. BuildTak) work best with the IR sensor.
# Persistent repeatablity issues are usually due to excessive ambient light.
# Try darkening the room or otherwise preventing light from interfering with
# the IR sensor on the extruder.
samples: 3
samples_tolerance: 0.2
samples_tolerance_retries: 5

[safe_z_home]
home_xy_position: 100, 70
speed: 150
z_hop: 10
z_hop_speed: 5

[bed_mesh]
speed: 200
horizontal_move_z: 5
mesh_min: 5, 30
mesh_max: 190, 195
probe_count: 4, 4
fade_start: 1
fade_end: 5

# Configured in Marlin as sensor 12:
# 100k 0603 SMD Vishay NTCS0603E3104FXT (4.7k pullup)
# (calibrated for Makibox hot bed)
[thermistor NTCS0603E3104FXT]
temperature1: 25
resistance1: 100000
temperature2: 50
resistance2: 29536
temperature3: 125
resistance3: 1908

[heater_bed]
heater_pin: PH5
sensor_type: NTCS0603E3104FXT
sensor_pin: PK6
control: pid
pid_kp: 72.263
pid_ki: 1.606
pid_kd: 812.963
min_temp: 0
max_temp: 125

[fan]
pin: PH4

[controller_fan board]
pin: PE3

[output_pin caselight]
pin: PH3
pwm: False
value: 1

[mcu]
serial: /dev/ttyACM0

# Uncomment the below if the host MCU is configured.
#[mcu host]
#serial: /tmp/klipper_host_mcu
#
# [filament_switch_sensor sensor0]
# switch_pin: ^!host:gpio21

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 20
max_z_accel: 100
