# This file contains pin mappings for the stock 2022 Sovol SV05
# with the 32-bit Creality 4.2.2 board.
#
# To use this config, during "make menuconfig" select the STM32F103
# with a "28KiB bootloader" and serial (on USART1 PA10/PA9)
# communication.
#
# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed. Might need a renaming if printer dosnt flash.

# See docs/Config_Reference.md for a description of parameters.
[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1000
minimum_cruise_ratio: 0.0
max_z_velocity: 5
max_z_accel: 100

[stepper_x]
step_pin: PC2
dir_pin: PB9
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA5
position_endstop: 220
position_max: 220
homing_speed: 50
position_min: -4

[stepper_y]
step_pin: PB8
dir_pin: PB7
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA6
position_endstop: 220
position_max: 220
position_min: -4
homing_speed: 50

[stepper_z]
step_pin: PB6
dir_pin: PB5
enable_pin: !PC3
microsteps: 16
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
position_max: 300
position_min: -3

[heater_bed]
heater_pin: PA2
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
min_temp: 0
max_temp: 130
control: pid
pid_kp: 64.742
pid_ki: 0.827
pid_kd: 1267.326

[fan]
pin: PA0

[safe_z_home]
home_xy_position: 70, 103
speed: 100
z_hop: 10
z_hop_speed: 20

[extruder]
step_pin: PB4
dir_pin: PB3
enable_pin: !PC3
microsteps: 16
rotation_distance: 7.394
nozzle_diameter: 0.400
filament_diameter: 1.750
max_extrude_only_distance: 100.0
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
min_temp: 0
max_temp: 280
control: pid
pid_kp: 28.850
pid_ki: 1.658
pid_kd: 125.496

[bltouch]
sensor_pin: ^PB1
control_pin: PB0
z_offset: 0
x_offset: 40
y_offset: 7
samples: 2
samples_tolerance: 0.015
samples_tolerance_retries: 5

[bed_mesh]
speed: 100
horizontal_move_z: 5
mesh_min: 40, 7
mesh_max: 220, 220
probe_count: 5, 5
algorithm: bicubic
fade_start: 1
fade_end: 10

[bed_screws]
screw1: 25,28
screw2: 195,28
screw3: 195,197
screw4: 25,197

[screws_tilt_adjust]
screw1: -4,21
screw1_name: front left screw
screw2: 155,21
screw2_name: front right screw
screw3: 155,190
screw3_name: rear right screw
screw4: -4,190
screw4_name: rear left screw
horizontal_move_z: 10
speed: 50
screw_thread: CW-M4

[display]
lcd_type: st7920
cs_pin: PB12
sclk_pin: PB13
sid_pin: PB15
encoder_pins: ^PB14, ^PB10
click_pin: ^!PB2
