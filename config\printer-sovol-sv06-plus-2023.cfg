# This file contains pin mappings for the stock Sovol SV06 Plus
# To use this config, during "make menuconfig" select the
# STM32F103 with a "28KiB bootloader" and serial (on USART1 PA10/PA9) communication.
# Also, since it is using the GD32F103, please select Disable <PERSON><PERSON> at startup
#
# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.
#
# Note: The stock LCD display does not currently work with Klipper
#
# See docs/Config_Reference.md for a description of parameters.
[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 2000
max_z_velocity: 10
max_z_accel: 100

[stepper_x]
step_pin: PC2
dir_pin: !PB9
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: tmc2209_stepper_x:virtual_endstop
position_endstop: 0
position_max: 305
homing_speed: 40
homing_retract_dist: 0

[tmc2209 stepper_x]
uart_pin: PC1
run_current: 0.860
sense_resistor: 0.150
uart_address: 3
driver_SGTHRS: 86
diag_pin: PA5

[stepper_y]
step_pin: PB8
dir_pin: PB7
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: tmc2209_stepper_y:virtual_endstop
position_endstop: 0
position_max: 305
homing_speed: 40
homing_retract_dist: 0

[tmc2209 stepper_y]
uart_pin: PC0
run_current: 0.900
sense_resistor: 0.150
uart_address: 3
driver_SGTHRS: 110
diag_pin: PA6

[stepper_z]
step_pin: PB6
dir_pin: !PB5
enable_pin: !PC3
microsteps: 16
rotation_distance: 4
endstop_pin: probe:z_virtual_endstop
position_min: -4
position_max: 350
homing_speed: 4

[tmc2209 stepper_z]
uart_pin: PA15
run_current: 1.000
interpolate: False
sense_resistor: 0.150
uart_address: 3
diag_pin: PA7

[extruder]
max_extrude_only_distance: 100.0
step_pin: PB4
dir_pin: !PB3
enable_pin: !PC3
microsteps: 16
rotation_distance: 4.56
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid
pid_kd: 41.96
pid_kp: 15.66
pid_ki: 1.49
min_temp: 0
max_temp: 300

[tmc2209 extruder]
uart_pin: PC14
run_current: 0.550
stealthchop_threshold: 0
interpolate: False
sense_resistor: 0.150
uart_address: 3

[heater_bed]
heater_pin: PA2
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_kp: 186.38
pid_ki: 36.12
pid_kd: 637.30
min_temp: 0
max_temp: 130

[fan]
pin: PA0

[probe]
pin: PB1
x_offset: 28
y_offset: -20
z_offset: 0

[safe_z_home]
home_xy_position: 123,170
z_hop: 10
z_hop_speed: 5

[bed_mesh]
speed: 120
mesh_min: 28, 20
mesh_max: 270, 270
probe_count: 5
algorithm: bicubic
fade_end: 10
fade_target: 0

[filament_switch_sensor filament_runout_sensor]
switch_pin: PA4
pause_on_runout: True
