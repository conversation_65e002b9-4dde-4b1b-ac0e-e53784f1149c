# This file contains pin mappings for the SUNLU S8 v1.01 (circa 2020), which
# is a modified RAMPS v1.3 board. To use this config, the firmware should be
# compiled for the AVR atmega2560. The following pins are available for
# expansion (e.g. ABL): ^PD2 (Z+ endstop), PG5, PE3, PH3, PB5

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE5
position_endstop: 0
position_max: 310
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PJ1
position_endstop: 0
position_max: 310
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PD3
position_endstop: 0.5
position_max: 400

[extruder]
step_pin: PA4
dir_pin: !PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.280
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_kp: 25.588
pid_ki: 1.496
pid_kd: 109.388
min_temp: 0
max_temp: 250

[filament_switch_sensor runout]
pause_on_runout: True
switch_pin: ^PE4

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: pid
pid_kp: 74.786
pid_ki: 0.766
pid_kd: 1825.718
min_temp: 0
max_temp: 110

[verify_heater heater_bed]
# The stock printer heats slowly due to a large bed and no external MOSFET.
# This should be reduced if an external MOSFET is added to increase max_temp
# and heating rate.
check_gain_time: 240

[fan]
pin: PH6

[heater_fan fan1]
pin: PH4

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 10
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PH1
sclk_pin: PA1
sid_pin: PH0
encoder_pins: ^PC4, ^PC6
click_pin: ^!PC2

[output_pin beeper]
pin: PC0
