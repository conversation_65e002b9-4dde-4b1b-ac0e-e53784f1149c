# Support for Tevo Flash. To use this config, the firmware should be
# compiled for the AVR atmega2560.

# Note, this config has only been tested on a modified Tevo Flash
# (using a Bondtech BMG extruder). If using a stock printer it may be
# necessary to update the extruder rotation_distance parameter.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: !PE5
position_endstop: -13
position_min: -13
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: !PJ1
position_endstop: -3
position_min: -3
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
position_max: 250
endstop_pin: probe:z_virtual_endstop
position_min: -2

[stepper_z1]
step_pin: PC1
dir_pin: PC3
enable_pin: !PC7
microsteps: 16
rotation_distance: 8

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
gear_ratio: 50:17
rotation_distance: 22.598
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 18.547
pid_Ki: 0.788
pid_Kd: 109.193
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: pid
pid_Kp: 38.824
pid_Ki: 0.539
pid_Kd: 698.838
min_temp: 0
max_temp: 70

[heater_fan heatbreak_cooling_fan]
pin: PH4

[fan]
pin: PH6

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: uc1701
cs_pin: PA3
a0_pin: PA5
encoder_pins: ^!PC6, ^!PC4
click_pin: ^!PC2
kill_pin: PK2

[bltouch]
sensor_pin: PD3
control_pin: PB5
x_offset: 0
y_offset: 18
z_offset: 1.64
samples: 3
sample_retract_dist: 5

# The homing_override section modifies the default G28 behavior
[homing_override]
set_position_z: 0
axes: z
gcode:
    G90
    G1 Z5 F600
    G28 X0 Y0
    G1 X118 Y118 F3600
    G28 Z0

# Mesh Bed Leveling.
[bed_mesh]
mesh_min: 5, 18
mesh_max: 230, 228
probe_count: 9, 9
algorithm: bicubic
