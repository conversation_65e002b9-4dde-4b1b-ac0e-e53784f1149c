# This file contains pin mappings for the Tronxy X5S (circa 2017). To
# use this config, the firmware should be compiled for the AVR
# atmega1284p.

# Note, a number of Melzi boards are shipped with a bootloader that
# requires the following command to flash the board:
#  avrdude -p atmega1284p -c arduino -b 57600 -P /dev/ttyUSB0 -U out/klipper.elf.hex
# If the above command does not work and "make flash" does not work
# then one may need to flash a bootloader to the board - see the
# Klipper docs/Bootloaders.md file for more information.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD7
dir_pin: !PC5
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC2
position_endstop: 0
position_max: 330
homing_speed: 50

[stepper_y]
step_pin: PC6
dir_pin: !PC7
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC3
position_endstop: 0
position_max: 310
homing_speed: 50

[stepper_z]
step_pin: PB3
dir_pin: PB2
enable_pin: !PD6
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PC4
position_endstop: 0.5
position_max: 400

[extruder]
step_pin: PB1
dir_pin: PB0
enable_pin: !PD6
microsteps: 16
rotation_distance: 35.520
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA7
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 275

[heater_bed]
heater_pin: PD4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA6
control: watermark
min_temp: 0
max_temp: 150

[verify_heater heater_bed]
# adjust for personal bed setup, this prevents stock heated bed from issuing
# false positive heating errors due to slow temperature increase
check_gain_time: 600

[fan]
pin: PB4

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: corexy
max_velocity: 300
max_accel: 1000
max_z_velocity: 20
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PA1
sclk_pin: PC0
sid_pin: PA3
encoder_pins: ^PD2, ^PD3
click_pin: ^!PA5
