# This is a Klipper configuration for TronXY X5SA Pro CoreXY Printer
# 330x330x400 size with CXY-V6 motherboard, Tronxy blue self-levelling plate sensor
# and stock Titan Extruder Clone.

###########################################################
# CAUTION: The Touchscreen cannot be used with Klipper!!! #
# You will lose all the display functionalities!!!        #
###########################################################

#            === FLASHING WITH STOCK BOOTLOADER ===
# You should make firmware for STM32F103 with bootloader offset
# at 0x8008800 (Chitu v6 Bootloader) and serial (on USART1 PA10/PA9)
# communication.

# Use "./scripts/update_chitu.py ./out/klipper.bin ./out/update.cbd"
# after make to generate update.cbd.  Put "update.cbd" file onto SD card,
# and reboot the printer.
# It will be automatically installed after you hear 2 beeps,
# and you will be able to update it this way.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: corexy
max_velocity: 300
max_accel: 3000
max_z_velocity: 25
max_z_accel: 30

[stepper_x]
step_pin: PE5
dir_pin: !PE6
enable_pin: !PC13
microsteps: 32
rotation_distance: 40
endstop_pin: !PG10
position_endstop: -1
position_min: -1
position_max: 350 # for bed mesh
homing_speed: 50
homing_retract_dist: 10
second_homing_speed: 10.0

[stepper_y]
step_pin: PE2
dir_pin: !PE3
enable_pin: !PE4
microsteps: 32
rotation_distance: 40
endstop_pin: !PA12
position_endstop: 0
position_max: 330
homing_retract_dist: 10
homing_speed: 50.0
second_homing_speed: 10.0

[stepper_z]
step_pin: PB9
dir_pin: PE0
enable_pin: !PE1
microsteps: 32
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
position_max: 400
position_min: -2

[extruder]
step_pin: PB4
dir_pin: !PB5
enable_pin: !PB8
microsteps: 32
rotation_distance: 22.478 # Titan Extruder Clone Rotation Distance
gear_ratio: 66:22 # Titan Extruder Clone Gear Ratio
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PG12
sensor_type: ATC Semitec 104GT-2
sensor_pin: PA1
control: pid
pid_Kp: 18.831
pid_Ki: 0.821
pid_Kd: 108.044
min_temp: 0
max_temp: 260
max_extrude_only_distance: 350

[heater_bed]
heater_pin: PG11
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
min_temp: 0
max_temp: 130
control: pid
pid_Kp: 73.932
pid_Ki: 1.521
pid_Kd: 898.279

[heater_fan hotend_fan]
pin: PG14

[fan]
pin: PG13

[controller_fan drivers_fan]
pin: PD6

[filament_switch_sensor filament_sensor]
pause_on_runout: True
runout_gcode:
  M25
switch_pin: !PA15

[output_pin beeper]
pin: PB0

[safe_z_home]
home_xy_position: 165, 165
speed: 50
z_hop: 10
z_hop_speed: 5

[bed_screws]
screw1: 5, 5
screw2: 165, 5
screw3: 325, 5
screw4: 5, 325
screw5: 165, 325
screw6: 325, 325

[bed_mesh]
speed: 120
probe_count: 5, 5
horizontal_move_z: 5
algorithm: lagrange
mesh_min : 20, 20
mesh_max : 310, 310
mesh_pps: 0

[probe]
x_offset: -35
y_offset: 0
pin: !PG9
speed: 30
z_offset: 1
