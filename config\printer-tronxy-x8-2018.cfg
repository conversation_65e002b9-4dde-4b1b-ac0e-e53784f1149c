# This file contains the configurations and pin mappings for the
# Tronxy X8 using the CXY-V2-0508 board.  To use this config file, the
# firmware should be compiled for the AVR ATmega1284p, 16MHz.

# Some Tronxy printers come without a bootloader present on the
# board. In that case, use MCUDude MightyCore ATmega1284p bootloader
# with TQFP44 Sanguino pinout. The package can be found at
# (https://github.com/MCUdude/MightyCore).  Follow <PERSON><PERSON><PERSON> install
# instructions but instead of "make flash FLASH_DEVICE=/dev/ttyACM0",
# use the following command:
# avrdude -p atmega1284p -c arduino -b 115200 -P /dev/ttyUSB0 -U out/klipper.elf.hex

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/ttyUSB0

[stepper_x]
step_pin: PD7
dir_pin: PC5
enable_pin: !PD6
microsteps: 16
rotation_distance: 32
endstop_pin: ^!PC2
position_endstop: -47
position_max: 220
position_min: -47
homing_speed: 50

[stepper_y]
step_pin: PC6
dir_pin: PC7
enable_pin: !PD6
microsteps: 16
rotation_distance: 32
endstop_pin: ^!PC3
position_endstop: 0
position_max: 220
position_min: 0
homing_speed: 50

[stepper_z]
step_pin: PB3
dir_pin: !PB2
enable_pin: !PD6
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PC4
position_endstop: 0
position_max: 210
homing_speed: 10

[extruder]
step_pin: PB1
dir_pin: PB0
enable_pin: !PD6
microsteps: 16
rotation_distance: 31.779
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA7
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 275

[heater_bed]
heater_pin: PD4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA6
control: watermark
max_delta: 2.0
min_temp: 0
max_temp: 150

[fan]
pin: PB4

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1000
max_z_velocity: 20
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PA1
sclk_pin: PC0
sid_pin: PA3
