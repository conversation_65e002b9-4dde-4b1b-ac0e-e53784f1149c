# This file contains common pin mappings for the Two Trees Sapphire
# Plus V1 (SP-5) printer (Robin Nano 1.2, 2208 drivers for X,Y and A4988 for Zs,E).

# INSTRUCTIONS FOR COMPILING
# To use this config, the firmware should be compiled for the STM32F103.
# When running "make menuconfig", enable "extra low-level configuration setup",
# select the 28KiB bootloader, serial (on USART3 PB11/PB10) to use USB communication
# or serial (on USART1 PA10/PA9) to use direct UART connection with Raspberry trough wifi pins.
# Set "GPIO pins to set at micro-controller startup" to "!PC6,!PD13" to turn off display at startup.

# INSTRUCTIONS FOR FLASHING, THE SCRIPT IS COMPULSORY OR IT WON'T WORK!!!
# Note that the "make flash" command does not work with the Robin Nano!
# After running "make", run the following command in one row FROM THE KLIPPER FOLDER:
#   ./scripts/update_mks_robin.py out/klipper.bin out/Robin_nano35.bin
# Copy the file out/Robin_nano35.bin (if you can't find the file the script was not executed)
# to an SD card and then restart the printer with that SD card.
# If you removed the LCD screen rename the file to "Robin_nano43.bin" for correct flashing.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE3
dir_pin: !PE2
enable_pin: !PE4
microsteps: 16
rotation_distance: 40
endstop_pin: !PA15
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_y]
step_pin: PE0
dir_pin: !PB9
enable_pin: !PE1
microsteps: 16
rotation_distance: 40
endstop_pin: !PA12
position_endstop: 300
position_max: 300
homing_speed: 50

[stepper_z]
step_pin: PB5
dir_pin: PB4
enable_pin: !PB8
microsteps: 16
rotation_distance: 8
endstop_pin: !PA11
position_endstop: 0
position_max: 340

[stepper_z1]
step_pin: PA6
dir_pin: PA1
enable_pin: !PA3
microsteps: 16
rotation_distance: 8

[extruder]
step_pin: PD6
dir_pin: !PD3
enable_pin: PB3
microsteps: 16
gear_ratio: 50:17
rotation_distance: 23.52
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC3
sensor_type: EPCOS 100K B57560G104F # Stock
sensor_pin: PC1
min_temp: 0
max_temp: 250
control: pid
pid_Kp: 17.48
pid_Ki: 1.32
pid_Kd: 57.81

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F # Stock
sensor_pin: PC0
min_temp: 0
max_temp: 130
control: pid
pid_Kp: 325.10
pid_Ki: 63.35
pid_Kd: 417.10

[heater_fan extruder]
pin: PB0

[fan]
pin: PB1

[mcu]
serial: /dev/ttyUSB0
restart_method: command

[printer]
kinematics: corexy
max_velocity: 250
max_accel: 4500
max_z_velocity: 15
max_z_accel: 100

[bed_screws]
screw1: 35,35
screw2: 275,35
screw3: 275,275
screw4: 35,275

[static_digital_output reset_display]
pins: !PC6, !PD13
