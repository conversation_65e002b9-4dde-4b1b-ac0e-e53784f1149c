# This file contains pin mappings for the Wanhao Duplicator 9 MK1,
# also sold as the Monoprice Maker Pro MK1. To use this config,
# the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF7
dir_pin: !PK0
enable_pin: !PF6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PF0
position_endstop: 0
position_max: 295
homing_speed: 30.0

[stepper_y]
step_pin: PK2
dir_pin: PK3
enable_pin: !PE4
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA2
position_endstop: 0
position_max: 290
homing_speed: 30.0

[stepper_z]
step_pin: PK5
dir_pin: PK7
enable_pin: !PK4
microsteps: 16
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
position_max: 370
position_min: -0.99

[extruder]
step_pin: PF4
dir_pin: PF5
enable_pin: !PF3
microsteps: 16
rotation_distance: 31.936
nozzle_diameter: 0.4
filament_diameter: 1.75
heater_pin: PG5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF1
control: pid
pid_Kp: 33.41
pid_Ki: 1.47
pid_Kd: 189.27
min_temp: 0
max_temp: 315

[heater_bed]
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: pid
pid_Kp: 64.095903
pid_Ki: 1.649830
pid_Kd: 622.531455
min_temp: 0
max_temp: 120

[fan]
pin: PE3

[probe]
pin: !PH3
x_offset: 27
y_offset: 3
z_offset: 1.4
speed: 5.0
samples: 2
sample_retract_dist: 2.0
samples_result: average

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 3000
max_accel: 450
max_z_velocity: 225
max_z_accel: 30
square_corner_velocity: 15.0

[bed_mesh]
speed: 120
mesh_min: 27, 3
mesh_max: 270, 290
probe_count: 5, 3
horizontal_move_z: 10

[bed_screws]
screw1: 5, 5
screw1_name: front left screw
screw2: 295, 0
screw2_name: front right screw
screw3: 295, 290
screw3_name: back right screw
screw4: 0, 290
screw4_name: back left screw
probe_height: 0
