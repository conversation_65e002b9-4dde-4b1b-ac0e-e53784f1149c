# This file contains pin mappings for the Wanhao Duplicator i3 mini.
# (circa 2017) To use this config, the firmware should be compiled
# for the AVR atmega2560.
# Pin numbers and other parameters were extracted from the official
# Marlin source available at: https://github.com/garychen99/i3Mini

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA0
dir_pin: !PA1
enable_pin: !PF3
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PD2
position_endstop: 120
position_max: 120
homing_speed: 30.0

[stepper_y]
step_pin: PA3
dir_pin: PA4
enable_pin: !PA2
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PD3
position_endstop: 0
position_max: 135
homing_speed: 30.0

[stepper_z]
step_pin: PA7
dir_pin: PG2
enable_pin: !PA6
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PD7
position_endstop: 0.5
position_max: 100

[extruder]
step_pin: PF1
dir_pin: !PF2
enable_pin: !PF0
microsteps: 16
rotation_distance: 34.043
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PG5
sensor_type: NTC 100K MGB18-104F39050L32
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 1
max_temp: 265

[fan]
pin: PB6

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: uc1701
cs_pin: PG0
a0_pin: PG1
rst_pin: PA5
click_pin: ^!PE3
encoder_pins: ^PE5, ^PE4
kill_pin: PK2
