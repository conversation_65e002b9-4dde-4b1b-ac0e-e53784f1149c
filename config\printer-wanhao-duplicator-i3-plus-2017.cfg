# This file contains pin mappings for the Wanhao Duplicator i3 Plus
# (circa 2017).  To use this config, the firmware should be compiled
# for the AVR atmega2560.
# Pin numbers and other parameters were extracted from the
# official Marlin source available at:
# https://github.com/garychen99/Duplicator-i3-plus

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF7
dir_pin: !PK0
enable_pin: !PF6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PF0
position_endstop: 0
position_max: 200
homing_speed: 30.0

[stepper_y]
step_pin: PK2
dir_pin: !PK3
enable_pin: !PK1
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA2
position_endstop: 0
position_max: 200
homing_speed: 30.0

[stepper_z]
step_pin: PK5
dir_pin: PK7
enable_pin: !PK4
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PA1
position_endstop: 0.5
position_max: 180

[extruder]
step_pin: PF4
dir_pin: PF5
enable_pin: !PF3
microsteps: 16
rotation_distance: 33.334
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PG5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF1
control: pid
pid_Kp: 30.850721
pid_Ki: .208175
pid_Kd: 192.298728
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: pid
pid_Kp: 64.095903
pid_Ki: 1.649830
pid_Kd: 622.531455
min_temp: 0
max_temp: 110

[fan]
pin: PE3

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 800
max_z_velocity: 5
max_z_accel: 100
