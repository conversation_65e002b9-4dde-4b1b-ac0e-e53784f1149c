# This file contains pin mappings for the Wanhao Duplicator i3 Plus
# Mark II. To use this config, the firmware should be compiled for the
# AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF7
dir_pin: !PK0
enable_pin: !PF6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PF0
position_endstop: 0
position_max: 200
homing_speed: 30.0

[stepper_y]
step_pin: PK2
dir_pin: !PK3
enable_pin: !PE4
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA2
position_endstop: 0
position_max: 200
homing_speed: 30.0

[stepper_z]
step_pin: PK5
dir_pin: PK7
enable_pin: !PK4
microsteps: 16
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
position_max: 180
position_min: -0.5

[extruder]
step_pin: PF4
dir_pin: PF5
enable_pin: !PF3
microsteps: 16
rotation_distance: 33.334
nozzle_diameter: 0.300
filament_diameter: 1.750
heater_pin: PG5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF1
control: pid
pid_Kp: 20.982
pid_Ki: 0.725
pid_Kd: 151.861
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 110

[fan]
pin: PE3

[probe]
pin: !PH3
z_offset: 1.0

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 800
max_z_velocity: 5
max_z_accel: 100

[bed_mesh]
mesh_min: 20, 20
mesh_max: 190, 130
probe_count: 4, 4
