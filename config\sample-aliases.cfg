# This file contains common board aliases for Arduino (and similar)
# boards.

# See docs/Config_Reference.md for a description of parameters.

# Arduino aliases for atmega168/328/328p boards
[board_pins arduino-standard]
aliases:
    ar0=PD0, ar1=PD1, ar2=PD2, ar3=PD3, ar4=PD4,
    ar5=PD5, ar6=PD6, ar7=PD7, ar8=PB0, ar9=PB1,
    ar10=PB2, ar11=PB3, ar12=PB4, ar13=PB5, ar14=PC0,
    ar15=PC1, ar16=PC2, ar17=PC3, ar18=PC4, ar19=PC5,
    analog0=PC0, analog1=PC1, analog2=PC2, analog3=PC3, analog4=PC4,
    analog5=PC5, analog6=PE2, analog7=PE3

# Arduino aliases for atmega2560/1280 (Arduino mega) boards
[board_pins arduino-mega]
aliases:
    ar0=PE0, ar1=PE1, ar2=PE4, ar3=PE5, ar4=PG5,
    ar5=PE3, ar6=PH3, ar7=PH4, ar8=PH5, ar9=PH6,
    ar10=PB4, ar11=PB5, ar12=PB6, ar13=PB7, ar14=PJ1,
    ar15=PJ0, ar16=PH1, ar17=PH0, ar18=PD3, ar19=PD2,
    ar20=PD1, ar21=PD0, ar22=PA0, ar23=PA1, ar24=PA2,
    ar25=PA3, ar26=PA4, ar27=PA5, ar28=PA6, ar29=PA7,
    ar30=PC7, ar31=PC6, ar32=PC5, ar33=PC4, ar34=PC3,
    ar35=PC2, ar36=PC1, ar37=PC0, ar38=PD7, ar39=PG2,
    ar40=PG1, ar41=PG0, ar42=PL7, ar43=PL6, ar44=PL5,
    ar45=PL4, ar46=PL3, ar47=PL2, ar48=PL1, ar49=PL0,
    ar50=PB3, ar51=PB2, ar52=PB1, ar53=PB0, ar54=PF0,
    ar55=PF1, ar56=PF2, ar57=PF3, ar58=PF4, ar59=PF5,
    ar60=PF6, ar61=PF7, ar62=PK0, ar63=PK1, ar64=PK2,
    ar65=PK3, ar66=PK4, ar67=PK5, ar68=PK6, ar69=PK7,
    analog0=PF0, analog1=PF1, analog2=PF2, analog3=PF3, analog4=PF4,
    analog5=PF5, analog6=PF6, analog7=PF7, analog8=PK0, analog9=PK1,
    analog10=PK2, analog11=PK3, analog12=PK4, analog13=PK5, analog14=PK6,
    analog15=PK7,
    # Marlin adds these additional aliases
    ml70=PG4, ml71=PG3, ml72=PJ2, ml73=PJ3, ml74=PJ7,
    ml75=PJ4, ml76=PJ5, ml77=PJ6, ml78=PE2, ml79=PE6,
    ml80=PE7, ml81=PD4, ml82=PD5, ml83=PD6, ml84=PH2,
    ml85=PH7

# Aliases for atmega644p (Sanguino boards)
[board_pins sanguino]
aliases:
    ar0=PB0, ar1=PB1, ar2=PB2, ar3=PB3, ar4=PB4,
    ar5=PB5, ar6=PB6, ar7=PB7, ar8=PD0, ar9=PD1,
    ar10=PD2, ar11=PD3, ar12=PD4, ar13=PD5, ar14=PD6,
    ar15=PD7, ar16=PC0, ar17=PC1, ar18=PC2, ar19=PC3,
    ar20=PC4, ar21=PC5, ar22=PC6, ar23=PC7, ar24=PA0,
    ar25=PA1, ar26=PA2, ar27=PA3, ar28=PA4, ar29=PA5,
    ar30=PA6, ar31=PA7,
    analog0=PA0, analog1=PA1, analog2=PA2, analog3=PA3, analog4=PA4,
    analog5=PA5, analog6=PA6, analog7=PA7

# Aliases for atsam3x8e (Arduino Due boards)
[board_pins arduino-due]
aliases:
    ar0=PA8, ar1=PA9, ar2=PB25, ar3=PC28, ar4=PA29,
    ar5=PC25, ar6=PC24, ar7=PC23, ar8=PC22, ar9=PC21,
    ar10=PA28, ar11=PD7, ar12=PD8, ar13=PB27, ar14=PD4,
    ar15=PD5, ar16=PA13, ar17=PA12, ar18=PA11, ar19=PA10,
    ar20=PB12, ar21=PB13, ar22=PB26, ar23=PA14, ar24=PA15,
    ar25=PD0, ar26=PD1, ar27=PD2, ar28=PD3, ar29=PD6,
    ar30=PD9, ar31=PA7, ar32=PD10, ar33=PC1, ar34=PC2,
    ar35=PC3, ar36=PC4, ar37=PC5, ar38=PC6, ar39=PC7,
    ar40=PC8, ar41=PC9, ar42=PA19, ar43=PA20, ar44=PC19,
    ar45=PC18, ar46=PC17, ar47=PC16, ar48=PC15, ar49=PC14,
    ar50=PC13, ar51=PC12, ar52=PB21, ar53=PB14, ar54=PA16,
    ar55=PA24, ar56=PA23, ar57=PA22, ar58=PA6, ar59=PA4,
    ar60=PA3, ar61=PA2, ar62=PB17, ar63=PB18, ar64=PB19,
    ar65=PB20, ar66=PB15, ar67=PB16, ar68=PA1, ar69=PA0,
    ar70=PA17, ar71=PA18, ar72=PC30, ar73=PA21, ar74=PA25,
    ar75=PA26, ar76=PA27, ar77=PA28, ar78=PB23,
    analog0=PA16, analog1=PA24, analog2=PA23, analog3=PA22, analog4=PA6,
    analog5=PA4, analog6=PA3, analog7=PA2, analog8=PB17, analog9=PB18,
    analog10=PB19, analog11=PB20

# Aliases for Adafruit GrandCentral boards (samd51)
[board_pins adafruit-grandcentral]
aliases:
    ar0=PB25, ar1=PB24, ar2=PC18, ar3=PC19, ar4=PC20,
    ar5=PC21, ar6=PD20, ar7=PD21, ar8=PB18, ar9=PB2,
    ar10=PB22, ar11=PB23, ar12=PB0, ar13=PB1, ar14=PB16,
    ar15=PB17, ar16=PC22, ar17=PC23, ar18=PB12, ar19=PB13,
    ar20=PB20, ar21=PB21, ar22=PD12, ar23=PA15, ar24=PC17,
    ar25=PC16, ar26=PA12, ar27=PA13, ar28=PA14, ar29=PB19,
    ar30=PA23, ar31=PA22, ar32=PA21, ar33=PA20, ar34=PA19,
    ar35=PA18, ar36=PA17, ar37=PA16, ar38=PB15, ar39=PB14,
    ar40=PC13, ar41=PC12, ar42=PC15, ar43=PC14, ar44=PC11,
    ar45=PC10, ar46=PC6, ar47=PC7, ar48=PC4, ar49=PC5,
    ar50=PD11, ar51=PD8, ar52=PD9, ar53=PD10, ar54=PA2,
    ar55=PA5, ar56=PB3, ar57=PC0, ar58=PC1, ar59=PC2,
    ar60=PC3, ar61=PB4, ar62=PB5, ar63=PB6, ar64=PB7,
    ar65=PB8, ar66=PB9, ar67=PA4, ar68=PA6, ar69=PA7,
    analog0=PA2, analog1=PA5, analog2=PB3, analog3=PC0, analog4=PC1,
    analog5=PC2, analog6=PC3, analog7=PB4, analog8=PB5, analog9=PB6,
    analog10=PB7, analog11=PB8, analog12=PB9, analog13=PA4, analog14=PA6,
    analog15=PA7

# Aliases for Beaglebone boards
[board_pins beaglebone]
aliases:
    P8_3=gpio1_6, P8_4=gpio1_7, P8_5=gpio1_2,
    P8_6=gpio1_3, P8_7=gpio2_2, P8_8=gpio2_3,
    P8_9=gpio2_5, P8_10=gpio2_4, P8_11=gpio1_13,
    P8_12=gpio1_12, P8_13=gpio0_23, P8_14=gpio0_26,
    P8_15=gpio1_15, P8_16=gpio1_14, P8_17=gpio0_27,
    P8_18=gpio2_1, P8_19=gpio0_22, P8_20=gpio1_31,
    P8_21=gpio1_30, P8_22=gpio1_5, P8_23=gpio1_4,
    P8_24=gpio1_1, P8_25=gpio1_0, P8_26=gpio1_29,
    P8_27=gpio2_22, P8_28=gpio2_24, P8_29=gpio2_23,
    P8_30=gpio2_25, P8_31=gpio0_10, P8_32=gpio0_11,
    P8_33=gpio0_9, P8_34=gpio2_17, P8_35=gpio0_8,
    P8_36=gpio2_16, P8_37=gpio2_14, P8_38=gpio2_15,
    P8_39=gpio2_12, P8_40=gpio2_13, P8_41=gpio2_10,
    P8_42=gpio2_11, P8_43=gpio2_8, P8_44=gpio2_9,
    P8_45=gpio2_6, P8_46=gpio2_7, P9_11=gpio0_30,
    P9_12=gpio1_28, P9_13=gpio0_31, P9_14=gpio1_18,
    P9_15=gpio1_16, P9_16=gpio1_19, P9_17=gpio0_5,
    P9_18=gpio0_4, P9_19=gpio0_13, P9_20=gpio0_12,
    P9_21=gpio0_3, P9_22=gpio0_2, P9_23=gpio1_17,
    P9_24=gpio0_15, P9_25=gpio3_21, P9_26=gpio0_14,
    P9_27=gpio3_19, P9_28=gpio3_17, P9_29=gpio3_15,
    P9_30=gpio3_16, P9_31=gpio3_14, P9_41=gpio0_20,
    P9_42=gpio3_20, P9_43=gpio0_7, P9_44=gpio3_18,
    P9_33=AIN4, P9_35=AIN6, P9_36=AIN5, P9_37=AIN2,
    P9_38=AIN3, P9_39=AIN0, P9_40=AIN1
