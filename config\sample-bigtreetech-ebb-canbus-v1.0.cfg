# This file contains common pin mappings for the BIGTREETECH EBBCan
# Canbus board. To use this config, the firmware should be compiled for the
# STM32F072 with "8 MHz crystal" and "USB (on PA11/PA12)" or "CAN bus (on PB8/PB9)".
# The "EBB Can" micro-controller will be used to control the components on the nozzle.

# See docs/Config_Reference.md for a description of parameters.

[mcu EBBCan]
serial: /dev/serial/by-id/usb-<PERSON><PERSON><PERSON>_Klipper_firmware_12345-if00
#canbus_uuid: 0e0d81e4210c

[adxl345]
cs_pin: EBBCan:PB12
spi_bus: spi2
axes_map: x,y,z

[extruder]
step_pin: EBBCan:PA9
dir_pin: !EBBCan:PA8
enable_pin: !EBBCan:PA10
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: EBBCan:PB1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: EBBCan:PA0
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

#sensor_type:MAX31865
#sensor_pin: EBBCan:PA15
#spi_bus: spi1a
#rtd_nominal_r: 100
#rtd_reference_r: 430
#rtd_num_of_wires: 2

[tmc2209 extruder]
uart_pin: EBBCan:PA13
run_current: 0.650
stealthchop_threshold: 999999

[fan]
pin: EBBCan:PA1

[heater_fan hotend_fan]
pin: EBBCan:PA2
heater: extruder
heater_temp: 50.0

#[neopixel hotend_rgb]
#pin: EBBCan:PA3

#[bltouch]
#sensor_pin: ^EBBCan:PA5
#control_pin: EBBCan:PA4

#[filament_switch_sensor switch_sensor]
#switch_pin: EBBCan:PB6

#[filament_motion_sensor motion_sensor]
#switch_pin: ^EBBCan:PB7
