# This file contains common pin mappings for the BIGTREETECH EBBCan
# Canbus board. To use this config, the firmware should be compiled for the
# STM32G0B1 with "8 MHz crystal" and "USB (on PA11/PA12)" or "CAN bus (on PB0/PB1)".
# The "EBB Can" micro-controller will be used to control the components on the nozzle.

# See docs/Config_Reference.md for a description of parameters.

[mcu EBBCan]
serial: /dev/serial/by-id/usb-<PERSON><PERSON><PERSON>_Klipper_firmware_12345-if00
#canbus_uuid: 0e0d81e4210c

[temperature_sensor EBB_NTC]
sensor_type: Generic 3950
sensor_pin: EBBCan:PA2

[adxl345]
cs_pin: EBBCan:PB12
spi_software_sclk_pin: EBBCan:PB10
spi_software_mosi_pin: EBBCan:PB11
spi_software_miso_pin: EBBCan:PB2
axes_map: x,y,z

[extruder]
step_pin: EBBCan:PD0
dir_pin: !EBBCan:PD1
enable_pin: !EBBCan:PD2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: EBBCan:PB13
sensor_type: EPCOS 100K B57560G104F
sensor_pin: EBBCan:PA3
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

# sensor_type:MAX31865
# sensor_pin: EBBCan:PA4
# spi_bus: spi1
# rtd_nominal_r: 100
# rtd_reference_r: 430
# rtd_num_of_wires: 2

[tmc2209 extruder]
uart_pin: EBBCan:PA15
run_current: 0.650
stealthchop_threshold: 999999

[fan]
pin: EBBCan:PA0

[heater_fan hotend_fan]
pin: EBBCan:PA1
heater: extruder
heater_temp: 50.0

#[heater_fan 4W_FAN0]
#pin: EBBCan:PB14
#tachometer_pin: EBBCan:PB15
#tachometer_ppr: 1

#[neopixel hotend_rgb]
#pin: EBBCan:PD3

#[bltouch]
#sensor_pin: ^EBBCan:PB8
#control_pin: EBBCan:PB9

## NPN and PNP proximity switch types can be set by jumper
#[probe]
#pin: ^EBBCan:PC13

#[output_pin PB5]
#pin: EBBCan:PB5

#[output_pin PB7]
#pin: EBBCan:PB7

#[output_pin PB6]
#pin: EBBCan:PB6
