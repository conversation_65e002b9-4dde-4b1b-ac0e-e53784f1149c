#Config for the BTT mot-exp
#See docs/Config_Reference.md for a description of parameters.

[stepper_1]
step_pin: EXP2_6
dir_pin: EXP2_5
enable_pin: !EXP2_7
microsteps: 16
rotation_distance: 40
position_max: 320
homing_speed: 50

[stepper_2]
step_pin: EXP2_3
dir_pin: EXP2_4
enable_pin: !EXP1_8
microsteps: 16
rotation_distance: 40
position_max: 300
homing_speed: 50

[stepper_3]
step_pin: EXP2_1
dir_pin: EXP2_2
enable_pin: !EXP1_7
microsteps: 16
rotation_distance: 8
position_endstop: 0.5
position_max: 400



[tmc2209 stepper_1]
uart_pin: EXP1_6
microsteps: 16
run_current: 0.800
stealthchop_threshold: 999999
diag_pin: EXP1_5

[tmc2209 stepper_2]
uart_pin: EXP1_4
microsteps: 16
run_current: 0.800
stealthchop_threshold: 999999
diag_pin: EXP1_3

[tmc2209 stepper_3]
uart_pin: EXP1_2
microsteps: 16
run_current: 0.650
stealthchop_threshold: 999999
diag_pin: EXP1_1
