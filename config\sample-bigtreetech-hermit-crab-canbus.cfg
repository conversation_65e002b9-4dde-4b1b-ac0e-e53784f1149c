# This file contains common pin mappings for the BIGTREETECH HermitCrab
# Canbus board. To use this config, the firmware should be compiled for the
# STM32F072 with "8 MHz crystal" and "USB (on PA11/PA12)" or "CAN bus (on PB8/PB9)".
# The "HermitCrab" micro-controller will be used to control the components on the nozzle.

# See docs/Config_Reference.md for a description of parameters.

[mcu HermitCrab]
serial: /dev/serial/by-id/usb-<PERSON><PERSON><PERSON>_Klipper_firmware_12345-if00
#canbus_uuid: 0e0d81e4210c

[adxl345]
cs_pin: HermitCrab:PB12
spi_bus: spi2
axes_map: y,z,-x

[extruder]
step_pin: HermitCrab:PA6
dir_pin: !HermitCrab:PA7
enable_pin: !HermitCrab:PA5
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: HermitCrab:PA2
sensor_type: EPCOS 100K B57560G104F
sensor_pin: HermitCrab:PA1
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[tmc2209 extruder]
uart_pin: HermitCrab:PB0
run_current: 0.650
stealthchop_threshold: 999999

[fan]
pin: HermitCrab:PA4

[heater_fan hotend_fan]
pin: HermitCrab:PA3
heater: extruder
heater_temp: 50.0

[neopixel hotend_rgb]
pin: HermitCrab:PA8
chain_count: 2
color_order: GRB
initial_RED: 0.5
initial_GREEN: 0.5
initial_BLUE: 0.5

#[bltouch]
#sensor_pin: ^HermitCrab:PB2
#control_pin: HermitCrab:PB1

#[filament_switch_sensor switch_sensor]
#switch_pin: HermitCrab:PA10

#[filament_motion_sensor motion_sensor]
#switch_pin: ^HermitCrab:PA9
