# This file contains a configuration snippet for a printer using two
# extruders that are selected by a servo.

# See docs/Config_Reference.md for a description of parameters.

# The primary extruder
[extruder]
step_pin: ar26
dir_pin: ar28
enable_pin: !ar24
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.500
filament_diameter: 3.500
heater_pin: ar10
sensor_type: EPCOS 100K B57560G104F
sensor_pin: analog13
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 210

# Script to change back to the main extruder
[gcode_macro T0]
gcode:
    SET_SERVO SERVO=extruder_servo angle=100    # Lift secondary extruder
    SET_GCODE_OFFSET Z=0 MOVE=1                 # Adjust z-height
    SET_GCODE_OFFSET X=0                        # Clear X offset
    ACTIVATE_EXTRUDER EXTRUDER=extruder

# Secondary extruder
[extruder1]
step_pin: ar36
dir_pin: ar34
enable_pin: !ar30
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.500
filament_diameter: 3.500
heater_pin: ar9
sensor_pin: analog15
sensor_type: EPCOS 100K B57560G104F
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 210

# Script to activate second extruder
[gcode_macro T1]
gcode:
    SET_GCODE_OFFSET Z=0.100 MOVE=1             # Adjust z-height
    SET_SERVO SERVO=extruder_servo angle=100    # Position second extruder
    SET_GCODE_OFFSET X=5                        # Account for different X offset
    ACTIVATE_EXTRUDER EXTRUDER=extruder1

# Servo definition
[servo extruder_servo]
pin: ar7
