# This file contains an example configuration with three
# micro-controllers simultaneously controlling a single printer.

# See docs/Config_Reference.md for a description of parameters.


# The main micro-controller is used as the timing source for all the
# micro-controllers on the printer. Typically, both the X and Y axes
# are connected to the main micro-controller.
[mcu]
serial: /dev/serial/by-path/platform-3f980000.usb-usb-0:1.2:1.0-port0

# The "zboard" micro-controller will be used to control the Z axis.
[mcu zboard]
serial: /dev/serial/by-path/platform-3f980000.usb-usb-0:1.3:1.0-port0

# The "auxboard" micro-controller will be used to control the heaters.
[mcu auxboard]
serial: /dev/serial/by-path/platform-3f980000.usb-usb-0:1.4:1.0-port0

[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE5
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: zboard:PL3
dir_pin: zboard:PL1
enable_pin: !zboard:PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^zboard:PD3
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: auxboard:PA4
dir_pin: auxboard:PA6
enable_pin: !auxboard:PA2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: auxboard:PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: auxboard:PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: auxboard:PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: auxboard:PK6
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: auxboard:PH6

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100
