# This file contains an example configuration with RPi as secondary mcu

# See docs/Config_Reference.md for a description of parameters.

# The rpi microcontroller is used as secondary.
# Typically, both the X and Y axes
# are connected to the main micro-controller. The rpi microcontroller is used
# on non time-critical functions such as enclosure sensors, additional fan or
# light sources
[mcu host]
serial: /tmp/klipper_host_mcu

# Example1: A led strip controlled by the GPIO20 on the RPi
[output_pin caselight]
pin: host:gpio20
# You can also write the pin in extended form by specifying
# the reference gpiochip.
#pin: host:gpiochip0/gpio20

[gcode_macro TOGGLE_CASELIGHT]
gcode:
  SET_PIN PIN=caselight VALUE={(not printer['output_pin caselight'].value)|int}


# Example2: Using the i2c bus of the RPi to read a sensor
[temperature_sensor enclosure_temp]
sensor_type: HTU21D
i2c_mcu: host
i2c_bus: i2c.1
htu21d_hold_master: False

[gcode_macro QUERY_ENCLOSURE]
gcode:
    {% set sensor = printer["htu21d enclosure_temp"] %}
    {action_respond_info(
        "Temperature: %.2f C\n"
        "Humidity: %.2f%%" % (
            sensor.temperature,
            sensor.humidity))}

# Example 3:Using a pin on another gpiochip connected to the RPi
# (in this case on an MCP23017)
[fan]
pin: host:gpiochip2/gpio7
