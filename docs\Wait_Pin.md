# WAIT_PIN: Hardware Handshake for 6-Axis Robotic Arm

The WAIT_PIN module provides blocking wait functionality for pin state changes, enabling precise hardware handshake between <PERSON><PERSON><PERSON> and external robotic arm controllers.

## Overview

In a 6-axis robotic arm 3D printing setup, the robotic arm controller handles motion planning while <PERSON><PERSON><PERSON> manages the toolhead (heater, extruder, fans). The WAIT_PIN command allows <PERSON><PERSON><PERSON> to wait for hardware signals from the robotic arm before executing extrusion or other tool operations.

## Configuration

Add the following to your printer configuration:

```ini
[wait_pin]
# Enable WAIT_PIN functionality
```

No additional configuration is required. The module will automatically handle pin monitoring as needed.

## G-code Command

### WAIT_PIN

Wait for a specified pin to reach a target state.

**Syntax:**
```
WAIT_PIN PIN=<pin_name> STATE=<0|1> [TIMEOUT=<seconds>]
```

**Parameters:**
- `PIN`: MCU pin name (e.g., "PA0", "PB1", "gpio_pin")
- `STATE`: Target state to wait for (0 = low, 1 = high)
- `TIMEOUT`: Maximum wait time in seconds (default: 30, range: 0.1-300)

**Examples:**
```gcode
# Wait for pin PA0 to go high, timeout after 10 seconds
WAIT_PIN PIN=PA0 STATE=1 TIMEOUT=10

# Wait for pin PB1 to go low, default timeout (30s)
WAIT_PIN PIN=PB1 STATE=0

# Wait for pin with custom timeout
WAIT_PIN PIN=gpio_pin STATE=1 TIMEOUT=5
```

## Hardware Setup

### Wiring

Connect the robotic arm controller's digital output (DOUT) to a Klipper MCU input pin:

```
Robotic Arm Controller    Klipper MCU
┌─────────────────────┐   ┌─────────────┐
│                     │   │             │
│  DOUT (3.3V/5V) ────┼───┤ PA0 (Input) │
│                     │   │             │
│  GND ───────────────┼───┤ GND         │
│                     │   │             │
└─────────────────────┘   └─────────────┘
```

### Pin Configuration

Ensure the input pin is properly configured in your MCU config. Most pins default to input with pull-up, but you can explicitly configure:

```ini
# In your printer.cfg, if needed
[static_digital_output pin_name]
pins: !PA0  # Use ! for inverted logic if needed
```

## Usage Examples

### Basic Synchronized Extrusion

```gcode
[gcode_macro SYNC_EXTRUDE]
gcode:
    {% set AMOUNT = params.AMOUNT|default(0.5)|float %}
    {% set PIN = params.PIN|default("PA0")|string %}
    
    # Wait for robotic arm signal
    WAIT_PIN PIN={PIN} STATE=1 TIMEOUT=10
    
    # Execute extrusion
    G1 E{AMOUNT} F300
    
    # Optional: Wait for signal to go low (end of extrusion window)
    WAIT_PIN PIN={PIN} STATE=0 TIMEOUT=5
```

### Multi-Point Printing Sequence

```gcode
[gcode_macro PRINT_SEQUENCE]
gcode:
    {% set POINTS = params.POINTS|default(5)|int %}
    {% set EXTRUDE_AMOUNT = params.AMOUNT|default(0.3)|float %}
    
    {% for i in range(POINTS) %}
        # Wait for arm to reach next position
        WAIT_PIN PIN=PA0 STATE=1 TIMEOUT=15
        
        # Extrude at this point
        G1 E{EXTRUDE_AMOUNT} F300
        
        # Wait for arm to signal ready for next point
        WAIT_PIN PIN=PA0 STATE=0 TIMEOUT=5
    {% endfor %}
```

### Temperature and Handshake Coordination

```gcode
[gcode_macro START_6AXIS_PRINT]
gcode:
    {% set EXTRUDER_TEMP = params.EXTRUDER_TEMP|default(200)|float %}
    {% set BED_TEMP = params.BED_TEMP|default(60)|float %}
    
    # Start heating
    M104 S{EXTRUDER_TEMP}
    M140 S{BED_TEMP}
    
    # Wait for temperatures
    M109 S{EXTRUDER_TEMP}
    M190 S{BED_TEMP}
    
    # Signal ready to robotic arm (if using output pin)
    # SET_PIN PIN=ready_signal VALUE=1
    
    # Wait for arm to signal start
    WAIT_PIN PIN=PA0 STATE=1 TIMEOUT=30
    
    RESPOND MSG="6-axis printing started"
```

## Error Handling

### Timeout Handling

If WAIT_PIN times out, it raises an error that stops the current G-code sequence:

```gcode
[gcode_macro SAFE_WAIT_EXTRUDE]
gcode:
    {% set PIN = params.PIN|default("PA0")|string %}
    {% set TIMEOUT = params.TIMEOUT|default(10)|float %}
    
    # This will stop execution if timeout occurs
    WAIT_PIN PIN={PIN} STATE=1 TIMEOUT={TIMEOUT}
    G1 E0.5 F300
```

### Testing Handshake

Use the test macro to verify handshake functionality:

```gcode
[gcode_macro TEST_HANDSHAKE]
gcode:
    {% set PIN = params.PIN|default("PA0")|string %}
    
    RESPOND MSG="Testing handshake on {PIN}..."
    RESPOND MSG="Please trigger the signal within 10 seconds"
    
    WAIT_PIN PIN={PIN} STATE=1 TIMEOUT=10
    RESPOND MSG="Signal received! Testing complete."
```

## Status Monitoring

Query the status of WAIT_PIN operations:

```gcode
# Check status via console or macro
{% if printer.wait_pin %}
    RESPOND MSG="Monitored pins: {printer.wait_pin.monitored_pins}"
{% endif %}
```

## Timing Considerations

### Typical Latencies

- **Pin state detection**: ~2-5ms (depends on MCU polling rate)
- **G-code processing**: ~1-10ms (depends on system load)
- **Total handshake latency**: ~5-15ms typical

### Optimization Tips

1. **Use dedicated pins**: Avoid sharing handshake pins with other functions
2. **Minimize timeout values**: Use realistic timeouts to catch issues quickly
3. **Test thoroughly**: Verify handshake timing under load conditions
4. **Consider signal duration**: Ensure robotic arm signals are long enough for reliable detection

## Troubleshooting

### Common Issues

**WAIT_PIN timeout errors:**
- Check wiring between robotic arm and MCU
- Verify pin names match configuration
- Ensure robotic arm is sending signals
- Check signal voltage levels (3.3V vs 5V compatibility)

**Inconsistent handshake:**
- Add debounce delay if needed
- Check for electrical noise on signal lines
- Verify ground connections
- Consider using shielded cables for long runs

**Performance issues:**
- Monitor system load during operation
- Check for other high-frequency operations
- Verify MCU is not overloaded

### Debug Commands

```gcode
# Test pin reading
QUERY_ENDSTOPS

# Check pin status (if configured as endstop)
# Or use multimeter to verify signal levels

# Test basic extrusion without handshake
G1 E1 F300
```

## Integration with Robotic Arm Controllers

### Signal Timing

The robotic arm controller should:
1. **Signal start**: Set DOUT high when ready for extrusion
2. **Hold signal**: Maintain high state during extrusion window
3. **Signal end**: Set DOUT low when extrusion should stop
4. **Repeat**: Continue sequence for next extrusion point

### Example Controller Logic (Pseudocode)

```
for each extrusion_point in path:
    move_to_position(extrusion_point)
    wait_for_position_reached()
    
    set_dout_high()  # Signal Klipper to extrude
    wait_for_extrusion_time()
    set_dout_low()   # Signal extrusion complete
    
    wait_before_next_move()
```

## Advanced Usage

### Multiple Handshake Pins

```gcode
# Use different pins for different operations
WAIT_PIN PIN=PA0 STATE=1  # Extrusion start
WAIT_PIN PIN=PA1 STATE=1  # Retraction signal
WAIT_PIN PIN=PA2 STATE=1  # Layer change signal
```

### Conditional Handshake

```gcode
[gcode_macro CONDITIONAL_EXTRUDE]
gcode:
    {% set USE_HANDSHAKE = params.HANDSHAKE|default(1)|int %}
    {% set AMOUNT = params.AMOUNT|default(0.5)|float %}
    
    {% if USE_HANDSHAKE %}
        WAIT_PIN PIN=PA0 STATE=1 TIMEOUT=10
    {% endif %}
    
    G1 E{AMOUNT} F300
```

This module enables precise coordination between robotic arm motion and Klipper tool operations, essential for high-quality 6-axis 3D printing.
