# Support for blocking wait on pin state changes for 6-axis robotic arm
#
# Copyright (C) 2024  Klipper 6-Axis Project
#
# This file may be distributed under the terms of the GNU GPLv3 license.

import logging
import threading
import time


class WaitPinMonitor:
    """Manages pin monitoring for a single pin"""
    def __init__(self, printer, pin_name):
        self.printer = printer
        self.pin_name = pin_name
        self.callbacks = []
        self.current_state = None
        self.last_eventtime = 0
        self.is_registered = False

        # Setup pin monitoring
        self._setup_pin()

    def _setup_pin(self):
        """Setup pin monitoring using buttons framework"""
        try:
            buttons = self.printer.load_object(
                self.printer.lookup_object('configfile'), 'buttons')

            # Create a minimal config for debounce
            class MinimalConfig:
                def get(self, key, default=None):
                    return default
                def getfloat(self, key, default=0.025, **kwargs):
                    return default

            config = MinimalConfig()
            buttons.register_debounce_button(
                self.pin_name, self._pin_callback, config)
            self.is_registered = True

        except Exception as e:
            raise Exception(f"Failed to setup pin {self.pin_name}: {e}")

    def _pin_callback(self, eventtime, state):
        """Handle pin state changes"""
        self.current_state = state
        self.last_eventtime = eventtime

        # Notify all waiting callbacks
        for callback in self.callbacks[:]:  # Copy list to avoid modification issues
            try:
                callback(eventtime, state)
            except Exception as e:
                logging.exception(f"Error in pin callback: {e}")

    def add_callback(self, callback):
        """Add a callback for pin state changes"""
        self.callbacks.append(callback)

    def remove_callback(self, callback):
        """Remove a callback"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)


class WaitPin:
    def __init__(self, config):
        self.printer = config.get_printer()
        self.reactor = self.printer.get_reactor()
        self.gcode = self.printer.lookup_object('gcode')

        # Pin monitors cache
        self.pin_monitors = {}
        self.monitor_lock = threading.Lock()

        # Register G-code command
        self.gcode.register_command("WAIT_PIN", self.cmd_WAIT_PIN,
                                   desc=self.cmd_WAIT_PIN_help)

    cmd_WAIT_PIN_help = ("Wait for pin to reach specified state. "
                        "Usage: WAIT_PIN PIN=<pin> STATE=<0|1> [TIMEOUT=<seconds>]")

    def cmd_WAIT_PIN(self, gcmd):
        # Parse parameters
        pin_name = gcmd.get('PIN')
        target_state = gcmd.get_int('STATE', minval=0, maxval=1)
        timeout = gcmd.get_float('TIMEOUT', 30.0, minval=0.1, maxval=300.0)

        if not pin_name:
            raise gcmd.error("WAIT_PIN requires PIN parameter")

        gcmd.respond_info(f"WAIT_PIN: Waiting for {pin_name} to reach state {target_state} "
                         f"(timeout: {timeout}s)")

        # Get or create pin monitor
        monitor = self._get_pin_monitor(pin_name)

        # Check if already in target state
        if monitor.current_state == target_state:
            gcmd.respond_info(f"WAIT_PIN: Pin {pin_name} already in state {target_state}")
            return

        # Setup wait condition
        wait_event = threading.Event()
        wait_success = [False]  # Use list for mutable reference

        def wait_callback(eventtime, state):
            if state == target_state:
                wait_success[0] = True
                wait_event.set()

        # Add callback and wait
        monitor.add_callback(wait_callback)

        try:
            # Wait for condition or timeout
            success = wait_event.wait(timeout)

            if success and wait_success[0]:
                gcmd.respond_info(f"WAIT_PIN: Pin {pin_name} reached state {target_state}")
            else:
                raise gcmd.error(f"WAIT_PIN: Timeout waiting for {pin_name} "
                               f"to reach state {target_state}")
        finally:
            # Remove callback
            monitor.remove_callback(wait_callback)

    def _get_pin_monitor(self, pin_name):
        """Get or create a pin monitor for the specified pin"""
        with self.monitor_lock:
            if pin_name not in self.pin_monitors:
                self.pin_monitors[pin_name] = WaitPinMonitor(self.printer, pin_name)
            return self.pin_monitors[pin_name]

    def get_status(self, eventtime):
        """Return status information"""
        with self.monitor_lock:
            pin_count = len(self.pin_monitors)
            pin_states = {}
            for pin_name, monitor in self.pin_monitors.items():
                pin_states[pin_name] = {
                    'current_state': monitor.current_state,
                    'last_eventtime': monitor.last_eventtime,
                    'active_callbacks': len(monitor.callbacks)
                }

        return {
            'monitored_pins': pin_count,
            'pin_states': pin_states
        }


def load_config(config):
    return WaitPin(config)
