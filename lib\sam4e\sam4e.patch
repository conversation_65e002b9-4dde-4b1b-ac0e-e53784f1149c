diff --git a/lib/sam4e/include/component/afec.h b/lib/sam4e/include/component/afec.h
index 34c4e61d..9a4f8f96 100644
--- a/lib/sam4e/include/component/afec.h
+++ b/lib/sam4e/include/component/afec.h
@@ -59,9 +59,9 @@ typedef struct {
   RoReg Reserved2[1];
   RwReg AFE_CDOR;      /**< \brief (Afec Offset: 0x5C) Channel DC Offset Register */
   RwReg AFE_DIFFR;     /**< \brief (Afec Offset: 0x60) Channel Differential Register */
-  RoReg AFE_CSELR;     /**< \brief (Afec Offset: 0x64) Channel Register Selection */
+  RwReg AFE_CSELR;     /**< \brief (Afec Offset: 0x64) Channel Register Selection */
   RoReg AFE_CDR;       /**< \brief (Afec Offset: 0x68) Channel Data Register */
-  RoReg AFE_COCR;      /**< \brief (Afec Offset: 0x6C) Channel Offset Compensation Register */
+  RwReg AFE_COCR;      /**< \brief (Afec Offset: 0x6C) Channel Offset Compensation Register */
   RwReg AFE_TEMPMR;    /**< \brief (Afec Offset: 0x70) Temperature Sensor Mode Register */
   RwReg AFE_TEMPCWR;   /**< \brief (Afec Offset: 0x74) Temperature Compare Window Register */
   RoReg Reserved3[7];
