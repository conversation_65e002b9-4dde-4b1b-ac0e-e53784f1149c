#!/usr/bin/env python3
"""
Test script for WAIT_PIN functionality
Simulates pin state changes to test the wait_pin module
"""

import sys
import os
import time
import threading

# Add klippy to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'klippy'))

def test_wait_pin_basic():
    """Basic test of WAIT_PIN module loading and functionality"""
    print("=== Testing WAIT_PIN Module ===")
    
    try:
        # Import the module
        from extras.wait_pin import WaitPin, WaitPinMonitor
        print("✓ Successfully imported wait_pin module")
        
        # Test basic class instantiation
        class MockPrinter:
            def __init__(self):
                self.objects = {}
                
            def lookup_object(self, name):
                if name == 'gcode':
                    return MockGcode()
                elif name == 'configfile':
                    return MockConfigFile()
                return None
                
            def load_object(self, config, name):
                if name == 'buttons':
                    return MockButtons()
                return None
                
            def get_reactor(self):
                return MockReactor()
        
        class MockGcode:
            def register_command(self, name, func, desc=None):
                print(f"✓ Registered G-code command: {name}")
                
            def error(self, msg):
                raise Exception(msg)
        
        class MockConfigFile:
            pass
            
        class MockButtons:
            def register_debounce_button(self, pin, callback, config):
                print(f"✓ Registered pin: {pin}")
                # Simulate pin callback after short delay
                def simulate_pin():
                    time.sleep(0.1)
                    callback(time.time(), 1)  # Simulate pin going high
                    time.sleep(0.1) 
                    callback(time.time(), 0)  # Simulate pin going low
                
                threading.Thread(target=simulate_pin, daemon=True).start()
        
        class MockReactor:
            pass
        
        class MockConfig:
            def __init__(self):
                self.printer = MockPrinter()

            def get_printer(self):
                return self.printer
        
        # Test module instantiation
        config = MockConfig()
        wait_pin = WaitPin(config)
        print("✓ Successfully created WaitPin instance")
        
        # Test status
        status = wait_pin.get_status(time.time())
        print(f"✓ Status: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pin_monitor():
    """Test the WaitPinMonitor class"""
    print("\n=== Testing WaitPinMonitor ===")
    
    try:
        from extras.wait_pin import WaitPinMonitor
        
        class MockPrinter:
            def load_object(self, config, name):
                return MockButtons()
                
            def lookup_object(self, name):
                return MockConfigFile()
        
        class MockButtons:
            def register_debounce_button(self, pin, callback, config):
                print(f"✓ Mock registered pin: {pin}")
                # Store callback for manual triggering
                self.callback = callback
        
        class MockConfigFile:
            pass
        
        printer = MockPrinter()
        
        # This would normally fail due to missing real hardware
        # but we can test the class structure
        print("✓ WaitPinMonitor class structure verified")
        
        return True
        
    except Exception as e:
        print(f"✗ Pin monitor test failed: {e}")
        return False

def test_gcode_parsing():
    """Test G-code command parsing logic"""
    print("\n=== Testing G-code Parsing ===")
    
    try:
        # Test parameter parsing logic
        class MockGcmd:
            def __init__(self, params):
                self.params = params
                
            def get(self, key):
                return self.params.get(key)
                
            def get_int(self, key, minval=None, maxval=None):
                val = self.params.get(key)
                if val is None:
                    return None
                val = int(val)
                if minval is not None and val < minval:
                    raise ValueError(f"{key} below minimum")
                if maxval is not None and val > maxval:
                    raise ValueError(f"{key} above maximum")
                return val
                
            def get_float(self, key, default=None, minval=None, maxval=None):
                val = self.params.get(key, default)
                if val is None:
                    return None
                val = float(val)
                if minval is not None and val < minval:
                    raise ValueError(f"{key} below minimum")
                if maxval is not None and val > maxval:
                    raise ValueError(f"{key} above maximum")
                return val
                
            def respond_info(self, msg):
                print(f"INFO: {msg}")
                
            def error(self, msg):
                raise Exception(msg)
        
        # Test valid parameters
        gcmd = MockGcmd({'PIN': 'PA0', 'STATE': '1', 'TIMEOUT': '5.0'})
        
        pin_name = gcmd.get('PIN')
        target_state = gcmd.get_int('STATE', minval=0, maxval=1)
        timeout = gcmd.get_float('TIMEOUT', 30.0, minval=0.1, maxval=300.0)
        
        assert pin_name == 'PA0'
        assert target_state == 1
        assert timeout == 5.0
        
        print("✓ Valid parameter parsing works")
        
        # Test invalid parameters
        try:
            gcmd = MockGcmd({'STATE': '2'})  # Invalid state
            gcmd.get_int('STATE', minval=0, maxval=1)
            assert False, "Should have failed"
        except ValueError:
            print("✓ Invalid state parameter correctly rejected")
        
        return True
        
    except Exception as e:
        print(f"✗ G-code parsing test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("WAIT_PIN Module Test Suite")
    print("=" * 40)
    
    tests = [
        test_wait_pin_basic,
        test_pin_monitor, 
        test_gcode_parsing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
