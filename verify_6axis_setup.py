#!/usr/bin/env python3
"""
Verification script for 6-axis robotic arm Klipper setup
Checks that all required components are present and functional
"""

import os
import sys
import importlib.util

def check_file_exists(path, description):
    """Check if a file exists"""
    if os.path.exists(path):
        print(f"✓ {description}: {path}")
        return True
    else:
        print(f"✗ {description}: {path} (NOT FOUND)")
        return False

def check_module_import(module_path, module_name, description):
    """Check if a Python module can be imported"""
    try:
        sys.path.insert(0, os.path.dirname(module_path))
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"✓ {description}: Module loads successfully")
        return True
    except Exception as e:
        print(f"✗ {description}: Import failed - {e}")
        return False

def check_kinematics():
    """Check that kinematics are properly cleaned up"""
    kinematics_dir = "klippy/kinematics"
    if not os.path.exists(kinematics_dir):
        print(f"✗ Kinematics directory not found: {kinematics_dir}")
        return False
    
    files = os.listdir(kinematics_dir)
    expected_files = {"__init__.py", "none.py", "extruder.py"}
    actual_files = set(f for f in files if f.endswith('.py'))
    
    if actual_files == expected_files:
        print(f"✓ Kinematics properly cleaned: {actual_files}")
        return True
    else:
        extra_files = actual_files - expected_files
        if extra_files:
            print(f"⚠ Extra kinematics files found: {extra_files}")
        missing_files = expected_files - actual_files
        if missing_files:
            print(f"✗ Missing kinematics files: {missing_files}")
            return False
        return True

def check_extras_cleanup():
    """Check that extras are properly cleaned up"""
    extras_dir = "klippy/extras"
    if not os.path.exists(extras_dir):
        print(f"✗ Extras directory not found: {extras_dir}")
        return False
    
    files = os.listdir(extras_dir)
    py_files = [f for f in files if f.endswith('.py') and f != '__init__.py']
    
    # Essential modules that should be present
    essential_modules = {
        'heaters.py', 'heater_bed.py', 'heater_generic.py',
        'fan.py', 'fan_generic.py', 'controller_fan.py',
        'extruder_stepper.py', 'manual_stepper.py', 'stepper_enable.py',
        'buttons.py', 'gcode_button.py', 'output_pin.py',
        'gcode_macro.py', 'gcode_move.py', 'thermistor.py',
        'wait_pin.py'  # Our new module
    }
    
    present_essential = set(py_files) & essential_modules
    missing_essential = essential_modules - set(py_files)
    
    print(f"✓ Extras modules present: {len(py_files)}")
    print(f"✓ Essential modules found: {len(present_essential)}/{len(essential_modules)}")
    
    if missing_essential:
        print(f"✗ Missing essential modules: {missing_essential}")
        return False
    
    # Check for modules that should have been removed
    removed_modules = {
        'bed_mesh.py', 'bed_tilt.py', 'input_shaper.py', 'resonance_tester.py',
        'display_status.py', 'pause_resume.py', 'print_stats.py'
    }
    
    still_present = set(py_files) & removed_modules
    if still_present:
        print(f"⚠ Modules that should be removed: {still_present}")
    
    return True

def check_config_files():
    """Check configuration files"""
    config_dir = "config"
    if not os.path.exists(config_dir):
        print(f"✗ Config directory not found: {config_dir}")
        return False
    
    # Check for our 6-axis config
    if os.path.exists("config/6axis-toolhead.cfg"):
        print("✓ 6-axis toolhead config present")
    else:
        print("✗ 6-axis toolhead config missing")
        return False
    
    # Check that printer configs were cleaned up
    files = os.listdir(config_dir)
    printer_configs = [f for f in files if f.startswith('printer-')]
    
    if len(printer_configs) == 0:
        print("✓ Printer config files cleaned up")
    else:
        print(f"⚠ {len(printer_configs)} printer config files still present")
    
    return True

def check_documentation():
    """Check documentation"""
    docs_dir = "docs"
    essential_docs = [
        "docs/Installation.md",
        "docs/Config_Reference.md", 
        "docs/G-Codes.md",
        "docs/Wait_Pin.md"  # Our new documentation
    ]
    
    all_present = True
    for doc in essential_docs:
        if os.path.exists(doc):
            print(f"✓ Documentation: {os.path.basename(doc)}")
        else:
            print(f"✗ Missing documentation: {doc}")
            all_present = False
    
    return all_present

def check_core_functionality():
    """Check that core Klipper functionality is intact"""
    core_files = [
        "klippy/klippy.py",
        "klippy/gcode.py", 
        "klippy/toolhead.py",
        "klippy/mcu.py",
        "klippy/pins.py",
        "src/Makefile",
        "src/Kconfig"
    ]
    
    all_present = True
    for file_path in core_files:
        if os.path.exists(file_path):
            print(f"✓ Core file: {os.path.basename(file_path)}")
        else:
            print(f"✗ Missing core file: {file_path}")
            all_present = False
    
    return all_present

def main():
    """Run all verification checks"""
    print("6-Axis Robotic Arm Klipper Setup Verification")
    print("=" * 50)
    
    checks = [
        ("Core Functionality", check_core_functionality),
        ("Kinematics Cleanup", check_kinematics),
        ("Extras Cleanup", check_extras_cleanup),
        ("Configuration Files", check_config_files),
        ("Documentation", check_documentation),
    ]
    
    # Check WAIT_PIN module specifically
    wait_pin_checks = [
        ("WAIT_PIN Module File", lambda: check_file_exists(
            "klippy/extras/wait_pin.py", "WAIT_PIN module")),
        ("WAIT_PIN Import", lambda: check_module_import(
            "klippy/extras/wait_pin.py", "wait_pin", "WAIT_PIN module import")),
        ("WAIT_PIN Documentation", lambda: check_file_exists(
            "docs/Wait_Pin.md", "WAIT_PIN documentation")),
        ("6-Axis Config", lambda: check_file_exists(
            "config/6axis-toolhead.cfg", "6-axis configuration")),
    ]
    
    all_checks = checks + [("WAIT_PIN Functionality", None)]
    
    passed = 0
    total = len(checks) + len(wait_pin_checks)
    
    # Run main checks
    for name, check_func in checks:
        print(f"\n--- {name} ---")
        if check_func():
            passed += 1
        else:
            print(f"✗ {name} check failed")
    
    # Run WAIT_PIN specific checks
    print(f"\n--- WAIT_PIN Functionality ---")
    wait_pin_passed = 0
    for name, check_func in wait_pin_checks:
        if check_func():
            wait_pin_passed += 1
        passed += 1 if check_func() else 0
    
    # Summary
    print(f"\n" + "=" * 50)
    print(f"VERIFICATION SUMMARY")
    print(f"=" * 50)
    print(f"Total checks: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    
    if passed == total:
        print(f"\n✅ ALL CHECKS PASSED!")
        print(f"Your 6-axis robotic arm Klipper setup is ready!")
        print(f"\nNext steps:")
        print(f"1. Configure your MCU and flash firmware")
        print(f"2. Update config/6axis-toolhead.cfg with your hardware pins")
        print(f"3. Test WAIT_PIN functionality with TEST_HANDSHAKE macro")
        print(f"4. Integrate with your robotic arm controller")
        return 0
    else:
        print(f"\n❌ SOME CHECKS FAILED!")
        print(f"Please review the failed checks above and fix any issues.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
